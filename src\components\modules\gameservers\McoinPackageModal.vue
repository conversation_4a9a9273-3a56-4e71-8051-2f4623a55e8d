<template>
  <Modal
    v-model="modalVisible"
    @close="handleClose"
    :title="modalTitle"
    width="500px"
  >
    <div class="p-6">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        @submit.prevent="handleSubmit"
        class="space-y-6"
        label-position="top"
        size="large"
      >
        <!-- Package Type Display -->
        <div class="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-900/50">
          <div class="flex items-center justify-center gap-2">
            <span class="text-sm text-gray-600 dark:text-gray-400">Loại gói:</span>
            <el-tag
              :type="packageType === 1 ? 'success' : 'warning'"
              size="large"
            >
              {{ typeLabels[packageType] }}
            </el-tag>
          </div>
        </div>

        <!-- Basic Information -->
        <div class="space-y-6">
          <!-- Name Field -->
          <FormField label="Tên gói" required :error="errors.name">
            <el-form-item prop="name" class="!mb-0">
              <el-input
                v-model="formData.name"
                :placeholder="namePlaceholder"
                :disabled="saving"
                clearable
                @input="handleNameInput"
              />
            </el-form-item>
          </FormField>

          <!-- Icon Upload Field -->
          <FormField label="Icon gói" :error="errors.icon">
            <AvatarUpload
              v-model="formData.iconPreview"
              :name="formData.name || 'Package Icon'"
              :disabled="saving"
              :accept="'image/*'"
              :max-size="2 * 1024 * 1024"
              :allowed-types="['image/jpeg', 'image/png', 'image/gif', 'image/webp']"
              @change="handleIconChange"
              @remove="handleIconRemove"
              @error="handleIconError"
            />
            <div class="mt-2 text-xs text-gray-500">
              Chọn ảnh icon cho gói (tối đa 2MB, định dạng: JPG, PNG, GIF, WebP)
            </div>
          </FormField>

          <!-- Game Selection Field -->
          <FormField label="Game" required :error="errors.game_id">
            <el-form-item prop="game_id" class="!mb-0">
              <el-select
                v-model="formData.game_id"
                placeholder="Chọn game"
                :disabled="saving || loadingGames"
                :loading="loadingGames"
                class="w-full"
                @change="handleGameChange"
              >
                <el-option
                  v-for="game in games"
                  :key="game.id"
                  :label="game.name"
                  :value="game.id"
                >
                  <div class="flex items-center gap-2">
                    <img v-if="game.thumb" :src="game.thumb" alt="" class="w-6 h-6 rounded" />
                    <span>{{ game.name }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </FormField>

          <!-- Required Value Field -->
          <FormField :label="requiredValueLabel" required :error="errors.required_value">
            <el-form-item prop="required_value" class="!mb-0">
              <el-input-number
                v-model="formData.required_value"
                :placeholder="requiredValuePlaceholder"
                :disabled="saving"
                :min="1"
                :max="999999999"
                :step="1"
                controls-position="right"
                class="w-full"
                @change="handleRequiredValueInput"
              />
            </el-form-item>
          </FormField>

          <!-- Received Value Field -->
          <FormField :label="receivedValueLabel" required :error="errors.received_value">
            <el-form-item prop="received_value" class="!mb-0">
              <el-input-number
                v-model="formData.received_value"
                :placeholder="receivedValuePlaceholder"
                :disabled="saving"
                :min="1"
                :max="999999999"
                :step="1"
                controls-position="right"
                class="w-full"
                @change="handleReceivedValueInput"
              />
            </el-form-item>
          </FormField>

          <!-- Status Field -->
          <FormField label="Trạng thái" required :error="errors.status">
            <el-form-item prop="status" class="!mb-0">
              <el-select
                v-model="formData.status"
                placeholder="Chọn trạng thái"
                :disabled="saving"
                class="w-full"
              >
                <el-option label="Đang hoạt động" :value="1" />
                <el-option label="Không hoạt động" :value="0" />
              </el-select>
            </el-form-item>
          </FormField>

          <!-- Meta Data Field -->
          <FormField label="Meta Data" :error="errors.meta_data">
            <el-form-item prop="meta_data" class="!mb-0">
              <el-input
                v-model="formData.meta_data"
                type="textarea"
                :rows="3"
                placeholder='Nhập meta data dạng JSON (VD: {"product_id":"123xxxx456"})'
                :disabled="saving"
              />
            </el-form-item>
            <div class="mt-2 text-xs text-gray-500">
              Meta data dạng JSON để lưu thông tin bổ sung (tùy chọn)
            </div>
          </FormField>

          <!-- Description Field -->
          <FormField label="Mô tả">
            <el-form-item prop="description" class="!mb-0">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="3"
                :placeholder="descriptionPlaceholder"
                :disabled="saving"
              />
            </el-form-item>
          </FormField>
        </div>

        <!-- Action Buttons -->
        <ButtonModalCommon
          :loading="saving"
          :can-submit="canSubmit"
          cancel-text="Hủy"
          :submit-text="editingPackage ? 'Cập nhật' : 'Thêm mới'"
          loading-text="Đang lưu..."
          @cancel="handleClose"
          @submit="handleSubmit"
        />
      </el-form>
    </div>
  </Modal>
</template>

<script setup>
import { ref, computed, watch, reactive, onMounted } from 'vue'
import { ElMessage, ElForm, ElFormItem, ElInput, ElInputNumber, ElSelect, ElOption, ElTag } from 'element-plus'
import Modal from '@/components/common/Modal.vue'
import FormField from '@/components/common/FormField.vue'
import ButtonModalCommon from '@/components/common/ButtonModalCommon.vue'
import AvatarUpload from '@/components/common/AvatarUpload.vue'
import { useFormValidation } from '@/composables/useFormValidation.js'
import { gamesApi } from '@/utils/apis/index.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  editingPackage: {
    type: Object,
    default: null
  },
  packageType: {
    type: Number,
    default: 1, // 1 = Nạp Mcoin | 2 = Đổi Mcoin sang xu
    validator: (value) => [1, 2].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'close', 'save', 'validation-error'])

// Composables
const { formRef, clearAllValidation, validateForm } = useFormValidation()

// State
const saving = ref(false)
const errors = ref({})
const games = ref([])
const loadingGames = ref(false)

// Type labels
const typeLabels = {
  1: 'Nạp Mcoin',
  2: 'Đổi Mcoin sang xu'
}

// Modal visibility computed property
const modalVisible = computed({
  get: () => {
    return props.modelValue
  },
  set: (value) => {
    emit('update:modelValue', value)
    if (!value) {
      emit('close')
    }
  }
})

// Modal title
const modalTitle = computed(() => {
  const action = props.editingPackage ? 'Chỉnh sửa' : 'Thêm'
  const packageTypeLabel = typeLabels[props.packageType].toLowerCase()
  return `${action} ${packageTypeLabel}`
})

// Dynamic placeholders
const namePlaceholder = computed(() => {
  const example = props.packageType === 1 ? 'Gói 50 Mcoin' : 'Gói đổi 50 xu'
  return `Nhập tên gói (VD: ${example})`
})

const descriptionPlaceholder = computed(() => {
  const packageTypeLabel = typeLabels[props.packageType]
  return `Nhập mô tả về ${packageTypeLabel} (tùy chọn)`
})

// Dynamic labels and placeholders for value fields
const requiredValueLabel = computed(() => {
  return props.packageType === 1 ? 'Giá trị yêu cầu (VNĐ)' : 'Giá trị yêu cầu (Mcoin)'
})

const receivedValueLabel = computed(() => {
  return props.packageType === 1 ? 'Giá trị nhận được (Mcoin)' : 'Giá trị nhận được (Xu)'
})

const requiredValuePlaceholder = computed(() => {
  return props.packageType === 1 ? 'Nhập số tiền VNĐ cần thanh toán' : 'Nhập số Mcoin cần có'
})

const receivedValuePlaceholder = computed(() => {
  return props.packageType === 1 ? 'Nhập số Mcoin nhận được' : 'Nhập số xu nhận được'
})

// Form data
const formData = reactive({
  name: '',
  icon: null,
  iconPreview: '',
  game_id: null,
  required_value: null,
  received_value: null,
  status: 1,
  meta_data: '',
  description: ''
})

// Validation rules
const rules = {
  name: [
    { required: true, message: 'Tên gói là bắt buộc', trigger: 'blur' },
    { min: 3, message: 'Tên gói phải có ít nhất 3 ký tự', trigger: 'change' }
  ],
  game_id: [
    { required: true, message: 'Vui lòng chọn game', trigger: 'change' }
  ],
  required_value: [
    { required: true, message: 'Giá trị yêu cầu là bắt buộc', trigger: 'blur' },
    { type: 'number', min: 1, message: 'Giá trị phải lớn hơn 0', trigger: 'change' }
  ],
  received_value: [
    { required: true, message: 'Giá trị nhận được là bắt buộc', trigger: 'blur' },
    { type: 'number', min: 1, message: 'Giá trị phải lớn hơn 0', trigger: 'change' }
  ],
  status: [
    { required: true, message: 'Trạng thái là bắt buộc', trigger: 'change' }
  ]
}

// Computed
const canSubmit = computed(() => {
  const nameValid = !!formData.name.trim()
  const gameValid = !!formData.game_id
  const requiredValueValid = formData.required_value && formData.required_value > 0
  const receivedValueValid = formData.received_value && formData.received_value > 0
  const statusValid = formData.status !== null && formData.status !== undefined

  return nameValid && gameValid && requiredValueValid && receivedValueValid && statusValid
})

// Input handlers for real-time validation
const handleNameInput = (value) => {
  formData.name = value
  
  // Clear validation if name is valid
  if (value && value.trim().length >= 3) {
    setTimeout(() => {
      if (formRef.value) {
        formRef.value.clearValidate('name')
      }
    }, 100)
  }
}

const handleRequiredValueInput = (value) => {
  formData.required_value = value

  // Clear validation if value is valid
  if (value && value > 0) {
    setTimeout(() => {
      if (formRef.value) {
        formRef.value.clearValidate('required_value')
      }
    }, 100)
  }
}

const handleReceivedValueInput = (value) => {
  formData.received_value = value

  // Clear validation if value is valid
  if (value && value > 0) {
    setTimeout(() => {
      if (formRef.value) {
        formRef.value.clearValidate('received_value')
      }
    }, 100)
  }
}

const handleGameChange = (gameId) => {
  formData.game_id = gameId

  // Clear validation if game is selected
  if (gameId) {
    setTimeout(() => {
      if (formRef.value) {
        formRef.value.clearValidate('game_id')
      }
    }, 100)
  }
}

const handleIconChange = (fileData) => {
  formData.icon = fileData.raw
  formData.iconPreview = fileData.preview
  errors.value.icon = ''
}

const handleIconRemove = () => {
  formData.icon = null
  formData.iconPreview = ''
  errors.value.icon = ''
}

const handleIconError = (errorMessage) => {
  errors.value.icon = errorMessage
}

// Watchers
watch(() => props.modelValue, (newShow) => {
  if (newShow) {
    resetForm()
    if (props.editingPackage) {
      loadPackageData()
    }
  }
})

// Lifecycle
onMounted(() => {
  loadGames()
})

// Methods
const loadGames = async () => {
  loadingGames.value = true
  try {
    const response = await gamesApi.getGames({ status: 1 }) // Only active games
    if (response?.data?.success) {
      games.value = response.data.data?.data?.map(game => ({
        id: game.id,
        name: game.name,
        thumb: game.thumb
      })) || []
    } else {
      games.value = []
      ElMessage.error('Không thể tải danh sách game')
    }
  } catch (error) {
    console.error('Error loading games:', error)
    games.value = []
    ElMessage.error('Có lỗi xảy ra khi tải danh sách game')
  } finally {
    loadingGames.value = false
  }
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    icon: null,
    iconPreview: '',
    game_id: null,
    required_value: null,
    received_value: null,
    status: 1,
    meta_data: '',
    description: ''
  })

  errors.value = {}

  // Clear form validation
  setTimeout(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  }, 100)
}

const loadPackageData = () => {
  if (props.editingPackage) {
    Object.assign(formData, {
      name: props.editingPackage.name || '',
      iconPreview: props.editingPackage.icon || '',
      game_id: props.editingPackage.game_id || null,
      required_value: props.editingPackage.required_value || null,
      received_value: props.editingPackage.received_value || null,
      status: props.editingPackage.status !== undefined ? props.editingPackage.status : 1,
      meta_data: props.editingPackage.meta_data || '',
      description: props.editingPackage.description || ''
    })
  }
}

const handleSubmit = async () => {
  if (!canSubmit.value) {
    ElMessage.error('Vui lòng kiểm tra lại thông tin')
    return
  }

  // Validate form
  try {
    await formRef.value.validate()
  } catch (error) {
    ElMessage.error('Vui lòng kiểm tra lại thông tin')
    return
  }

  saving.value = true

  try {
    // Prepare form data
    const submitData = {
      name: formData.name.trim(),
      game_id: formData.game_id,
      type: props.packageType,
      required_value: formData.required_value,
      received_value: formData.received_value,
      status: formData.status
    }

    // Add meta_data if provided
    if (formData.meta_data?.trim()) {
      submitData.meta_data = formData.meta_data.trim()
    }

    // Add description if provided
    if (formData.description?.trim()) {
      submitData.description = formData.description.trim()
    }

    // Add icon if provided
    if (formData.icon) {
      submitData.icon = formData.icon
    }

    emit('save', submitData)
  } catch (error) {
    console.error('Submit error:', error)
    ElMessage.error('Có lỗi xảy ra khi lưu dữ liệu')
    saving.value = false
  }
  // Note: saving.value = false will be handled by parent component after successful API call
}

const handleClose = () => {
  emit('close')
}

// Method to handle server validation errors
const handleServerValidationErrors = (serverErrors) => {
  errors.value = {}

  if (serverErrors && typeof serverErrors === 'object') {
    Object.keys(serverErrors).forEach(field => {
      if (serverErrors[field] && Array.isArray(serverErrors[field]) && serverErrors[field].length > 0) {
        errors.value[field] = serverErrors[field][0] // Get first error message
      }
    })
  }
}

// Expose methods and state for parent component
defineExpose({
  handleServerValidationErrors,
  saving
})
</script>

<style lang="scss" scoped>
// Remove form item margins to use FormField spacing
:deep(.el-form-item) {
  margin-bottom: 0 !important;
}

// Error state styling for form validation
:deep(.el-form-item.is-error .el-input__wrapper) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

:deep(.el-form-item.is-error .el-textarea__inner) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

:deep(.el-form-item.is-error .el-select .el-input__wrapper) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

:deep(.el-form-item.is-error .el-input-number .el-input__wrapper) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

// Input number full width styling
:deep(.el-input-number.w-full) {
  width: 100% !important;
}

:deep(.el-input-number.w-full .el-input) {
  width: 100% !important;
}

:deep(.el-input-number.w-full .el-input__wrapper) {
  width: 100% !important;
}
</style>
