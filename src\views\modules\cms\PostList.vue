<template>
  <div class="post-list-wrapper">
    <PageBreadcrumb :page-title="currentPageTitle" :breadcrumbs="[{ label: 'Quản lý CMS', to: '/cms' }]" />

    <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-800 dark:bg-white/[0.03]">
      <!-- Filters -->
      <div class="mb-6 rounded-lg border border-gray-200 bg-gray-50 p-6 dark:border-gray-700 dark:bg-gray-900/50">
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <el-input
              v-model="searchTerm"
              placeholder="Tìm kiếm theo tiêu đề bài viết..."
              :prefix-icon="SearchIcon"
              @clear="handleResetFilters"
              clearable
              style="width: 300px"
              size="large"
            />
          </div>

          <div class="flex items-center gap-2">
            <el-select
              v-model="selectedStatus"
              placeholder="Lọc theo trạng thái"
              @change="handleStatusFilter"
              clearable
              style="width: 200px"
              size="large"
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>

          <div class="flex items-center gap-2">
            <el-select
              v-model="selectedCategory"
              placeholder="Lọc theo danh mục"
              @change="handleCategoryFilter"
              clearable
              style="width: 200px"
              size="large"
            >
              <el-option
                v-for="option in categoryOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>

          <div class="flex items-center gap-2">
            <el-select
              v-model="selectedHot"
              placeholder="Lọc bài nổi bật"
              @change="handleHotFilter"
              clearable
              style="width: 160px; height: 40px"
              size="large"
            >
              <el-option label="Tất cả" value="" />
              <el-option label="Nổi bật" value="1" />
              <el-option label="Không nổi bật" value="0" />
            </el-select>
          </div>

          <div class="ml-auto flex items-center gap-3">
            <router-link to="/cms/posts/create">
              <ButtonCommon type="primary" :icon="PlusIcon"> Tạo Bài viết </ButtonCommon>
            </router-link>
          </div>
        </div>
      </div>

      <!-- Posts Table -->
      <div
        class="table-container overflow-hidden rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800"
      >
        <el-table
          v-loading="loading"
          :data="posts"
          style="width: 100%"
          :header-cell-style="getTableHeaderStyle"
          :cell-style="getTableCellStyle"
          table-layout="auto"
          class="post-table"
          :row-class-name="getRowClassName"
          empty-text="Chưa có bài viết nào"
        >
          <!-- Combined Image and Title Column -->
          <el-table-column label="Bài viết" min-width="150">
            <template #default="{ row }">
              <div class="flex items-start gap-3 p-2">
                <!-- Image -->
                <div class="flex-shrink-0">
                  <img
                    v-if="row.cover_image"
                    :src="row.cover_image"
                    alt=""
                    class="h-16 w-20 rounded-lg object-cover"
                    @error="handleImageError($event)"
                  />
                  <div
                    v-else
                    class="flex h-16 w-20 items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gradient-to-br from-gray-100 to-gray-200 dark:border-gray-600 dark:from-gray-700 dark:to-gray-800"
                  >
                    <Picture class="h-6 w-6 text-gray-400 dark:text-gray-500" />
                  </div>
                </div>

                <!-- Content -->
                <div class="min-w-0 flex-1">
                  <!-- Title -->
                  <div class="mb-1 font-medium text-gray-900 dark:text-white">
                    <router-link
                      :to="`/cms/posts/${row.id}`"
                      class="transition-colors hover:text-blue-600 dark:hover:text-blue-400"
                    >
                      {{ row.title }}
                    </router-link>
                  </div>

                  <!-- Description -->
                  <div class="line-clamp-1 text-sm text-gray-600 dark:text-gray-400">
                    {{ row.excerpt || row.description || 'Không có mô tả' }}
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- Category Column -->
          <el-table-column label="Danh mục" width="140" align="center">
            <template #default="{ row }">
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {{ row.category?.name || '—' }}
              </span>
            </template>
          </el-table-column>

          <!-- Status Column with Dropdown -->
          <el-table-column label="Trạng thái" width="180" align="center">
            <template #default="{ row }">
              <el-select
                :model-value="row.status"
                @update:model-value="handleStatusChange(row.id, 'status', $event)"
                size="small"
                style="width: 140px"
                :loading="row.statusLoading"
                :disabled="row.statusLoading"
              >
                <el-option
                  v-for="option in tableStatusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </template>
          </el-table-column>

          <!-- Hot Status Column -->
          <el-table-column label="Nổi bật" width="115" align="center">
            <template #default="{ row }">
              <div style="display: flex; justify-content: center; align-items: center">
                <el-switch
                  :model-value="Boolean(row.is_hot)"
                  @update:model-value="handleStatusChange(row.id, 'is_hot', $event)"
                  size="small"
                  :loading="row.hotLoading"
                  :disabled="row.hotLoading"
                />
              </div>
            </template>
          </el-table-column>

          <!-- Date Column -->
          <el-table-column label="Ngày tạo" width="145" align="center">
            <template #default="{ row }">
              <div class="text-sm text-gray-600 dark:text-gray-300">
                {{ formatDate(row.created_at) }}
              </div>
            </template>
          </el-table-column>

          <!-- Actions Column -->
          <el-table-column label="Thao tác" width="200" fixed="right" align="center">
            <template #default="{ row }">
              <ActionButtons
                :edit-to="`/cms/posts/edit/${row.id}`"
                @view="viewPost(row)"
                @delete="deletePost(row.id)"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- Pagination -->
      <Pagination
        :pagination="pagination"
        :per-page-options="[10, 15, 20, 50]"
        @update:pagination="handlePaginationUpdate"
        @page-change="handlePageChange"
        @per-page-change="handlePerPageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onActivated, onUnmounted, watch } from 'vue'
import { Edit, Delete, Search, Picture } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { usePosts } from '@/composables/modules/cms/usePosts.js'
import { PlusIcon, RefreshIcon, EyeIcon, EditIcon, TrashIcon } from '@/components/icons/index.js'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import Pagination from '@/components/common/Pagination.vue'
import ActionButtons from '@/components/common/ActionButtons.vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import { hasPermission, getUserPermissions } from '@/utils/helpers/permission.helper.js'
import { useAuthStore } from '@/state/index.js'
import { storeToRefs } from 'pinia'
import { formatDate } from '@/utils/helpers/datetime.helper.js'

// Icons
const SearchIcon = Search

// Page title
const currentPageTitle = ref('Quản lý Bài viết')

// Composables
const {
  loading,
  posts,
  pagination,
  searchParams,
  fetchPosts,
  deletePost: deletePostApi,
  updatePostStatus,
  fetchCategories,
  getStatusOptions,
  getCategoryOptions,
  getStatusLabel,
  getStatusType,
} = usePosts()

// Refs & reactive data
const searchTerm = ref('')
const selectedStatus = ref('')
const selectedCategory = ref('')
const selectedHot = ref('')
const isMounted = ref(false)

// Computed
const statusOptions = computed(() => getStatusOptions())
const categoryOptions = computed(() => getCategoryOptions())

// Status options for table dropdowns (without "Tất cả" option)
const tableStatusOptions = computed(() => getStatusOptions().filter((option) => option.value !== ''))

// Pagination computed for the Pagination component
const paginationInfo = computed(() => ({
  current_page: searchParams.page,
  per_page: searchParams.limit,
  total: pagination.value.total || 0,
  from: pagination.value.from || 0,
  to: pagination.value.to || 0,
}))

// Methods
const handleStatusFilter = () => {
  // Temporarily disable to prevent unwanted triggers
  isMounted.value = false
  searchParams.status = selectedStatus.value
  searchParams.page = 1
  fetchPosts().then(() => {
    setTimeout(() => {
      isMounted.value = true
    }, 200)
  })
}

const handleCategoryFilter = () => {
  // Temporarily disable to prevent unwanted triggers
  isMounted.value = false
  searchParams.category_id = selectedCategory.value
  searchParams.page = 1
  fetchPosts().then(() => {
    setTimeout(() => {
      isMounted.value = true
    }, 200)
  })
}

const handleHotFilter = () => {
  // Temporarily disable to prevent unwanted triggers
  isMounted.value = false
  searchParams.is_hot = selectedHot.value
  searchParams.page = 1
  fetchPosts().then(() => {
    setTimeout(() => {
      isMounted.value = true
    }, 200)
  })
}

const handleResetFilters = () => {
  // Temporarily disable mounted check to prevent triggers during reset
  isMounted.value = false

  searchTerm.value = ''
  selectedStatus.value = ''
  selectedCategory.value = ''
  selectedHot.value = ''
  searchParams.title = ''
  searchParams.status = ''
  searchParams.category_id = ''
  searchParams.is_hot = ''
  searchParams.page = 1

  fetchPosts().then(() => {
    // Re-enable after a short delay
    setTimeout(() => {
      isMounted.value = true
    }, 200)
  })
}

const handleStatusChange = async (id, field, value) => {
  // Chỉ thực hiện nếu component đã mount hoàn toàn
  if (!isMounted.value) {
    return
  }

  // Tìm post hiện tại để so sánh giá trị
  const currentPost = posts.value.find((p) => p.id === id)
  if (!currentPost) {
    return
  }

  // Xử lý so sánh giá trị cho is_hot (boolean vs number)
  let currentValue = currentPost[field]
  let newValue = value

  if (field === 'is_hot') {
    // Chuẩn hóa giá trị boolean cho việc so sánh
    currentValue = Boolean(currentValue)
    newValue = Boolean(value)
  }

  // Chỉ update nếu giá trị thực sự thay đổi
  if (currentValue === newValue) {
    return
  }

  // Set loading state cho field cụ thể
  const loadingField = field === 'status' ? 'statusLoading' : field === 'is_hot' ? 'hotLoading' : 'loading'
  currentPost[loadingField] = true

  try {
    await updatePostStatus(id, field, value)
  } catch (error) {
    console.error('Error updating post attribute:', error)
    // Error đã được handle trong composable
  } finally {
    // Remove loading state
    currentPost[loadingField] = false
  }
}

// Pagination handlers for the new Pagination component
const handlePaginationUpdate = (newPagination) => {
  searchParams.page = newPagination.current_page
  searchParams.limit = newPagination.per_page
}

const handlePageChange = async (page) => {
  searchParams.page = page
  await fetchPosts()
}

const handlePerPageChange = async (perPage) => {
  searchParams.limit = perPage
  searchParams.page = 1
  await fetchPosts()
}

const deletePost = async (id) => {
  await deletePostApi(id)
}

const viewPost = (post) => {
  // Navigate to post detail page
  window.open(`/cms/posts/${post.id}`, '_blank')
}

// Handle image error
const handleImageError = (event) => {
  const target = event.target
  target.style.display = 'none'
  // Show placeholder when image fails to load
  const placeholder = target.nextElementSibling
  if (!placeholder) {
    const placeholderDiv = document.createElement('div')
    placeholderDiv.className =
      'h-16 w-20 rounded-lg bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600'
    placeholderDiv.innerHTML =
      '<svg class="h-6 w-6 text-gray-400 dark:text-gray-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" /></svg>'
    target.parentNode.appendChild(placeholderDiv)
  }
}

// Enhanced table styles based on existing patterns
const getTableHeaderStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color-page)',
    color: 'var(--el-text-color-primary)',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: '14px',
    padding: '16px 12px',
    borderBottom: '1px solid var(--el-border-color-light)',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
  }
}

const getTableCellStyle = () => {
  return {
    backgroundColor: 'var(--el-fill-color-blank)',
    color: 'var(--el-text-color-primary)',
    borderBottom: '1px solid var(--el-border-color-lighter)',
    padding: '16px 12px',
    fontSize: '14px',
  }
}

const getRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// Auto search when input changes
let searchTimeout = null
watch(searchTerm, (newValue) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  searchTimeout = setTimeout(() => {
    // Temporarily disable to prevent unwanted triggers
    isMounted.value = false
    searchParams.title = newValue?.trim() || ''
    searchParams.page = 1
    fetchPosts().then(() => {
      setTimeout(() => {
        isMounted.value = true
      }, 200)
    })
  }, 300)
})

// Initialize
onMounted(async () => {
  // Clear any existing messages
  ElMessage.closeAll()
  await Promise.all([fetchPosts(), fetchCategories()])

  // Enable switches after a short delay to prevent initial triggers
  setTimeout(() => {
    isMounted.value = true
  }, 500)
})

// Cleanup when component unmounts
onUnmounted(() => {
  // Clear any pending messages
  ElMessage.closeAll()
})

const authStore = useAuthStore()
const { authUser } = storeToRefs(authStore)
const userPermissions = getUserPermissions(authUser.value)

const canCreatePost = hasPermission(['post_management.create'], userPermissions)
const canEditPost = hasPermission(['post_management.edit'], userPermissions)
const canDeletePost = hasPermission(['post_management.delete'], userPermissions)
</script>

<style lang="scss">
@use '@/assets/styles/modules/cms/post-list';

/* Text truncation utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom styles for improved post list layout */
.post-table :deep(.el-table__row) {
  height: auto;
  min-height: 80px;
}

.post-table :deep(.el-table__cell) {
  vertical-align: middle;
}

/* Ensure dropdown fits well - Force smaller height */
.post-table :deep(.el-table .el-table__body .el-table__row .el-table__cell .el-select) {
  width: 140px !important;
  height: 20px !important;
}

.post-table :deep(.el-table .el-table__body .el-table__row .el-table__cell .el-select .el-input__wrapper) {
  min-height: 20px !important;
  height: 20px !important;
  font-size: 11px !important;
  padding: 0 6px !important;
  border-radius: 4px !important;
}

.post-table :deep(.el-table .el-table__body .el-table__row .el-table__cell .el-select .el-input__inner) {
  font-size: 11px !important;
  height: 20px !important;
  line-height: 20px !important;
}

.post-table :deep(.el-table .el-table__body .el-table__row .el-table__cell .el-select .el-input) {
  height: 20px !important;
}

.post-table :deep(.el-table .el-table__body .el-table__row .el-table__cell .el-select .el-select__caret) {
  font-size: 10px !important;
  line-height: 20px !important;
}

.post-table :deep(.el-table .el-table__body .el-table__row .el-table__cell .el-select .el-select__wrapper) {
  height: 20px !important;
}

/* Make switch smaller */
.post-table :deep(.el-switch) {
  --el-switch-on-color: #409eff;
  --el-switch-off-color: #dcdfe6;
}

/* Table empty state styling */
.post-table :deep(.el-table__empty-block) {
  background-color: var(--el-bg-color);
  border: none;
  padding: 40px 20px;
}

.post-table :deep(.el-table__empty-text) {
  color: var(--el-text-color-regular);
  font-size: 14px;
  font-weight: 500;
}

/* Dark mode empty state */
.dark .post-table :deep(.el-table__empty-block) {
  background-color: #1e293b;
}

.dark .post-table :deep(.el-table__empty-text) {
  color: #94a3b8;
}
</style>
