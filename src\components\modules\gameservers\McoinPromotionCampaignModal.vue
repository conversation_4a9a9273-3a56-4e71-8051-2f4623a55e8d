<template>
  <Modal
    :modelValue="modelValue"
    :title="isEditing ? 'Sửa chiến dịch khuyến mại' : 'Thêm chiến dịch khuyến mại'"
    width="500px"
    @update:modelValue="$emit('update:modelValue', $event)"
    @close="handleClose"
  >
    <div class="p-6">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-position="top"
        class="mcoin-promotion-campaign-form space-y-6"
        @submit.prevent="handleSubmit"
        size="large"
      >
        <!-- Campaign Name -->
        <FormField label="Tên chiến dịch" required>
          <el-form-item prop="name" class="!mb-0">
            <el-input
              v-model="formData.name"
              placeholder="Nhập tên chiến dịch..."
              maxlength="100"
              show-word-limit
              clearable
            />
          </el-form-item>
        </FormField>

        <!-- Campaign Description -->
        <FormField label="Mô tả" required>
          <el-form-item prop="description" class="!mb-0">
            <el-input
              v-model="formData.description"
              type="textarea"
              placeholder="Nhập mô tả chiến dịch..."
              :rows="3"
              maxlength="500"
              show-word-limit
              resize="none"
            />
          </el-form-item>
        </FormField>

        <!-- Start Date -->
        <FormField label="Ngày bắt đầu" required>
          <el-form-item prop="start_date" class="!mb-0">
            <el-date-picker
              v-model="formData.start_date"
              type="datetime"
              placeholder="Chọn ngày bắt đầu"
              format="DD/MM/YYYY HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledStartDate"
              :default-time="new Date(2000, 1, 1, 0, 0, 0)"
              class="w-full"
            />
          </el-form-item>
        </FormField>

        <!-- End Date -->
        <FormField label="Ngày kết thúc" required>
          <el-form-item prop="end_date" class="!mb-0">
            <el-date-picker
              v-model="formData.end_date"
              type="datetime"
              placeholder="Chọn ngày kết thúc"
              format="DD/MM/YYYY HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledEndDate"
              :default-time="new Date(2000, 1, 1, 23, 59, 59)"
              class="w-full"
            />
          </el-form-item>
        </FormField>

        <!-- Bonus Type -->
        <FormField label="Loại ưu đãi" required>
          <el-form-item prop="bonus_type" class="!mb-0">
            <el-select
              v-model="formData.bonus_type"
              placeholder="Chọn loại ưu đãi"
              class="w-full"
              @change="handleBonusTypeChange"
            >
              <el-option
                v-for="option in bonusTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </FormField>

        <!-- Bonus Value -->
        <FormField label="Giá trị ưu đãi" required>
          <el-form-item prop="bonus_value" class="!mb-0">
            <el-input-number
              v-model="formData.bonus_value"
              :placeholder="getBonusPlaceholder()"
              :min="getBonusMin()"
              :max="getBonusMax()"
              :precision="0"
              controls-position="right"
              class="w-full"
            />
          </el-form-item>
        </FormField>

        <!-- Bonus Preview -->
        <FormField label="Xem trước ưu đãi">
          <div class="bonus-preview p-3 bg-gray-50 rounded-lg border dark:bg-gray-800 dark:border-gray-700">
            <el-tag 
              :type="getBonusTagType(formData.bonus_type)" 
              size="large"
              class="font-medium"
            >
              {{ formatBonusPreview() }}
            </el-tag>
          </div>
        </FormField>

        <!-- Status -->
        <FormField label="Trạng thái" required>
          <el-form-item prop="status" class="!mb-0">
            <el-select
              v-model="formData.status"
              placeholder="Chọn trạng thái"
              class="w-full"
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
                :disabled="option.disabled"
              />
            </el-select>
          </el-form-item>
        </FormField>

        <!-- Active Campaign Warning -->
        <el-alert
          v-if="formData.status === 'active' && hasActiveCampaign && !isEditing"
          title="Cảnh báo"
          type="warning"
          :description="activeCampaignWarning"
          show-icon
          :closable="false"
          class="mb-4"
        />

        <!-- Action Buttons -->
        <ButtonModalCommon
          :loading="saving"
          cancel-text="Hủy"
          :submit-text="isEditing ? 'Cập nhật' : 'Thêm mới'"
          loading-text="Đang lưu..."
          @cancel="handleClose"
          @submit="handleSubmit"
        />
      </el-form>
    </div>
  </Modal>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import Modal from '@/components/common/Modal.vue'
import FormField from '@/components/common/FormField.vue'
import ButtonModalCommon from '@/components/common/ButtonModalCommon.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  editingCampaign: {
    type: Object,
    default: null
  },
  campaigns: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'close', 'save'])

// Refs
const formRef = ref(null)
const saving = ref(false)

// Computed
const isEditing = computed(() => !!props.editingCampaign)

const hasActiveCampaign = computed(() => {
  return props.campaigns.some(campaign => 
    campaign.status === 'active' && 
    (!isEditing.value || campaign.id !== props.editingCampaign?.id)
  )
})

const activeCampaignWarning = computed(() => {
  const activeCampaign = props.campaigns.find(campaign => 
    campaign.status === 'active' && 
    (!isEditing.value || campaign.id !== props.editingCampaign?.id)
  )
  return activeCampaign 
    ? `Hiện tại chiến dịch "${activeCampaign.name}" đang hoạt động. Khi bạn lưu chiến dịch này với trạng thái "Đang hoạt động", chiến dịch hiện tại sẽ được tự động chuyển sang trạng thái "Tạm dừng".`
    : ''
})

// Form data
const formData = ref({
  name: '',
  description: '',
  start_date: '',
  end_date: '',
  bonus_type: 1,
  bonus_value: 0,
  status: 'paused'
})

// Options
const bonusTypeOptions = [
  { label: 'Phần trăm (%)', value: 1 },
  { label: 'Số xu cố định', value: 2 },
  { label: 'Item đặc biệt', value: 3 }
]

const statusOptions = computed(() => [
  { label: 'Đang hoạt động', value: 'active' },
  { label: 'Tạm dừng', value: 'paused' },
  { 
    label: 'Đã kết thúc', 
    value: 'ended',
    disabled: !isEditing.value // Only allow "ended" when editing
  }
])

// Form rules
const formRules = {
  name: [
    { required: true, message: 'Vui lòng nhập tên chiến dịch', trigger: 'blur' },
    { min: 3, max: 100, message: 'Tên chiến dịch phải từ 3-100 ký tự', trigger: 'blur' }
  ],
  description: [
    { required: true, message: 'Vui lòng nhập mô tả chiến dịch', trigger: 'blur' },
    { min: 10, max: 500, message: 'Mô tả phải từ 10-500 ký tự', trigger: 'blur' }
  ],
  start_date: [
    { required: true, message: 'Vui lòng chọn ngày bắt đầu', trigger: 'change' }
  ],
  end_date: [
    { required: true, message: 'Vui lòng chọn ngày kết thúc', trigger: 'change' },
    { validator: validateEndDate, trigger: 'change' }
  ],
  bonus_type: [
    { required: true, message: 'Vui lòng chọn loại ưu đãi', trigger: 'change' }
  ],
  bonus_value: [
    { required: true, message: 'Vui lòng nhập giá trị ưu đãi', trigger: 'blur' },
    { validator: validateBonusValue, trigger: 'blur' }
  ],
  status: [
    { required: true, message: 'Vui lòng chọn trạng thái', trigger: 'change' }
  ]
}

// Validation functions
function validateEndDate(rule, value, callback) {
  if (!value) {
    callback(new Error('Vui lòng chọn ngày kết thúc'))
    return
  }
  
  if (!formData.value.start_date) {
    callback(new Error('Vui lòng chọn ngày bắt đầu trước'))
    return
  }
  
  const startDate = new Date(formData.value.start_date)
  const endDate = new Date(value)
  
  if (endDate <= startDate) {
    callback(new Error('Ngày kết thúc phải sau ngày bắt đầu'))
    return
  }
  
  callback()
}

function validateBonusValue(rule, value, callback) {
  if (value === null || value === undefined || value === '') {
    callback(new Error('Vui lòng nhập giá trị ưu đãi'))
    return
  }
  
  const min = getBonusMin()
  const max = getBonusMax()
  
  if (value < min || value > max) {
    callback(new Error(`Giá trị ưu đãi phải từ ${min} đến ${max}`))
    return
  }
  
  callback()
}

// Date validation
const disabledStartDate = (time) => {
  // Disable past dates (only for new campaigns)
  if (!isEditing.value) {
    return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
  }
  return false
}

const disabledEndDate = (time) => {
  // Disable dates before start date
  if (formData.value.start_date) {
    const startDate = new Date(formData.value.start_date)
    return time.getTime() <= startDate.getTime()
  }
  
  // Disable past dates (only for new campaigns)
  if (!isEditing.value) {
    return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
  }
  
  return false
}

// Bonus helpers
const getBonusMin = () => {
  switch (formData.value.bonus_type) {
    case 1: return 1  // percentage: 1-100%
    case 2: return 1  // coins: 1-10000
    case 3: return 1  // item: any positive number
    default: return 1
  }
}

const getBonusMax = () => {
  switch (formData.value.bonus_type) {
    case 1: return 100   // percentage: 1-100%
    case 2: return 10000 // coins: 1-10000
    case 3: return 999999 // item: large number
    default: return 100
  }
}

const getBonusPlaceholder = () => {
  switch (formData.value.bonus_type) {
    case 1: return 'Nhập % (1-100)'
    case 2: return 'Nhập số xu (1-10000)'
    case 3: return 'Nhập Item ID'
    default: return 'Nhập giá trị'
  }
}

const getBonusTagType = (bonusType) => {
  switch (bonusType) {
    case 1: return 'success'
    case 2: return 'warning'  
    case 3: return 'info'
    default: return ''
  }
}

const formatBonusPreview = () => {
  if (!formData.value.bonus_value || formData.value.bonus_value <= 0) {
    return 'Chưa có giá trị'
  }
  
  switch (formData.value.bonus_type) {
    case 1:
      return `+${formData.value.bonus_value}%`
    case 2:
      return `+${formData.value.bonus_value} xu`
    case 3:
      return `Item ID: ${formData.value.bonus_value}`
    default:
      return 'N/A'
  }
}

const handleBonusTypeChange = () => {
  // Reset bonus value when type changes
  formData.value.bonus_value = 0
  
  // Clear validation for bonus_value field
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate('bonus_value')
    }
  })
}

// Modal handlers
const handleClose = () => {
  emit('close')
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    // Validate form
    await formRef.value.validate()
    
    saving.value = true
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Emit save event
    emit('save', { ...formData.value })
    
  } catch (error) {
    console.error('Form validation failed:', error)
  } finally {
    saving.value = false
  }
}

// Reset form data
const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    start_date: '',
    end_date: '',
    bonus_type: 1,
    bonus_value: 0,
    status: 'paused'
  }
  
  // Clear form validation
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

// Load form data when editing
const loadFormData = (campaign) => {
  if (campaign) {
    formData.value = {
      name: campaign.name || '',
      description: campaign.description || '',
      start_date: campaign.start_date || '',
      end_date: campaign.end_date || '',
      bonus_type: campaign.bonus_type || 1,
      bonus_value: campaign.bonus_value || 0,
      status: campaign.status || 'paused'
    }
  } else {
    resetForm()
  }
}

// Watchers
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    loadFormData(props.editingCampaign)
  }
})

watch(() => props.editingCampaign, (newValue) => {
  if (props.modelValue) {
    loadFormData(newValue)
  }
})

// Watch start_date changes to revalidate end_date
watch(() => formData.value.start_date, () => {
  if (formData.value.end_date && formRef.value) {
    nextTick(() => {
      formRef.value.validateField('end_date')
    })
  }
})
</script>

<style lang="scss" scoped>
// Remove form item margins to use FormField spacing
:deep(.el-form-item) {
  margin-bottom: 0 !important;
}

.mcoin-promotion-campaign-form {
  .bonus-preview {
    min-height: 40px;
    display: flex;
    align-items: center;
  }
}

// Error state styling for form validation
:deep(.el-form-item.is-error .el-input__wrapper) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

:deep(.el-form-item.is-error .el-textarea__inner) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

:deep(.el-form-item.is-error .el-select .el-input__wrapper) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

:deep(.el-form-item.is-error .el-input-number .el-input__wrapper) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

/* Date picker full width */
:deep(.el-date-editor.w-full) {
  width: 100% !important;
}

/* Input number full width */
:deep(.el-input-number.w-full) {
  width: 100% !important;
}

:deep(.el-input-number.w-full .el-input) {
  width: 100% !important;
}

:deep(.el-input-number.w-full .el-input__wrapper) {
  width: 100% !important;
}

/* Select full width */
:deep(.el-select.w-full) {
  width: 100% !important;
}
</style>
