import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { transactionsApi } from '@/utils/apis/index.js'

export function useTransactions() {
  const cards = ref([])
  const pagination = reactive({
    current_page: 1,
    per_page: 15,
    total: 0,
    last_page: 1,
    from: 1,
    to: 0,
    has_more_pages: false,
  })

  const fetchCards = async (params = {}) => {
    try {
      const apiParams = {
        page: params.page || pagination.current_page,
        limit: params.per_page || pagination.per_page,
      }
      const response = await transactionsApi.getCards(apiParams)
      if (response?.data?.success === true) {
        const responseData = response.data.data
        cards.value = responseData.data
        Object.assign(pagination, responseData.pagination)
      }
    } catch (error) {
      console.error('Error fetching cards:', error)
      ElMessage.error(error.response?.data?.message || 'Không thể tải danh sách thẻ')
    }
  }

  const createCard = async (cardData) => {
    try {
      const apiData = {
        amount: cardData.amount,
        quantity: cardData.quantity,
        note: cardData.note,
      }
      const response = await transactionsApi.createCard(apiData)
      if (response?.data?.success === true) {
        ElMessage.success(response.data.message || 'Thêm thẻ thành công')
        fetchCards()
      }
    } catch (error) {
      ElMessage.error(error.response?.data?.message || 'Không thể tạo thẻ')
    }
  }

  const filterCards = (keyword = '') => {
    if (!keyword) return cards.value

    const searchTerm = keyword.toLowerCase().trim()
    return cards.value.filter((card) => (card.serial || card.pin || card.note).toLowerCase().includes(searchTerm))
  }

  const cancelCard = async (cardId) => {
    try {
      const response = await transactionsApi.cancelCard(cardId)
      if (response?.data?.success === true) {
        ElMessage.success(response.data.message || 'Hủy thẻ thành công')
        fetchCards()
      }
    } catch (error) {
      ElMessage.error(error.response?.data?.message || 'Không thể hủy thẻ')
    }
  }

  return {
    cards,
    pagination,
    fetchCards,
    createCard,
    filterCards,
    cancelCard,
  }
}
