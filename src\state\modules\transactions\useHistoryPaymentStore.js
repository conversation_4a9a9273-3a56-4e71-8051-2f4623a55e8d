import { defineStore } from 'pinia'

const generateDummyData = () => {
  const data = []
  const statuses = ['success', 'pending', 'failed', 'cancelled']
  const methods = ['mcoin_card', 'credit_card', 'atm', 'qr_code']
  for (let i = 1; i <= 100; i++) {
    const amount = [10000, 20000, 50000, 100000, 200000, 500000][Math.floor(Math.random() * 6)]
    data.push({
      id: i,
      transactionId: `TXN${Date.now() - Math.random() * 1000000}`,
      timestamp: new Date(Date.now() - Math.random() * 1000 * 3600 * 24 * 60).toLocaleString('vi-VN'),
      account: `user_${Math.floor(Math.random() * 1000)}`,
      amount: amount,
      mcoin: amount / 100, // Example conversion rate
      paymentMethod: methods[Math.floor(Math.random() * methods.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
    })
  }
  return data
}

export const useHistoryPaymentStore = defineStore('historyPayment', {
  state: () => ({
    history: [],
    loading: false,
    filters: {
      search: '',
      status: '',
      paymentMethod: '',
    },
    sort: {
      prop: 'id',
      order: 'descending',
    },
    pagination: {
      current_page: 1,
      per_page: 15,
      total: 0,
    },
  }),

  getters: {
    filteredHistory(state) {
      let data = [...state.history]

      // Filter by search term
      if (state.filters.search) {
        const searchTerm = state.filters.search.toLowerCase()
        data = data.filter(
          (d) => d.transactionId.toLowerCase().includes(searchTerm) || d.account.toLowerCase().includes(searchTerm),
        )
      }

      // Filter by status
      if (state.filters.status) {
        data = data.filter((d) => d.status === state.filters.status)
      }

      // Filter by payment method
      if (state.filters.paymentMethod) {
        data = data.filter((d) => d.paymentMethod === state.filters.paymentMethod)
      }

      // Apply sorting
      data.sort((a, b) => {
        const aVal = a[state.sort.prop]
        const bVal = b[state.sort.prop]
        const modifier = state.sort.order === 'ascending' ? 1 : -1
        if (aVal < bVal) return -1 * modifier
        if (aVal > bVal) return 1 * modifier
        return 0
      })

      return data
    },

    paginatedHistory(state) {
      const start = (state.pagination.current_page - 1) * state.pagination.per_page
      const end = start + state.pagination.per_page
      return this.filteredHistory.slice(start, end)
    },

    paginationInfo(state) {
      const total = this.filteredHistory.length
      const start = (state.pagination.current_page - 1) * state.pagination.per_page
      return {
        total,
        from: total > 0 ? start + 1 : 0,
        to: Math.min(start + state.pagination.per_page, total),
      }
    },
  },

  actions: {
    async fetchHistory() {
      this.loading = true
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500))
      this.history = generateDummyData()
      this.pagination.total = this.history.length
      this.loading = false
    },

    setFilters(newFilters) {
      this.filters = { ...this.filters, ...newFilters }
      this.pagination.current_page = 1 // Reset to first page on filter change
    },

    setSort(newSort) {
      this.sort = newSort
    },

    setPage(page) {
      this.pagination.current_page = page
    },
  },
})
