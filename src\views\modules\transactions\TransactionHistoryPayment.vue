<template>
  <section class="flex h-screen flex-col">
    <PageBreadcrumb :page-title="currentPageTitle" :breadcrumbs="[{ label: 'Trang chủ', to: '/' }]" />

    <div
      class="mb-4 flex flex-1 flex-col rounded-lg border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-600 dark:bg-gray-700/50"
    >
      <div class="mb-4 flex-shrink-0">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <el-input
            :model-value="store.filters.search"
            placeholder="Tìm mã GD, tài khoản..."
            clearable
            @update:model-value="store.setFilters({ search: $event })"
          />

          <el-select
            :model-value="store.filters.status"
            placeholder="Trạng thái"
            clearable
            @update:model-value="store.setFilters({ status: $event })"
          >
            <el-option label="Thành công" value="success" />
            <el-option label="Chờ xử lý" value="pending" />
            <el-option label="Thất bại" value="failed" />
            <el-option label="Đã hủy" value="cancelled" />
          </el-select>
          <el-select
            :model-value="store.filters.paymentMethod"
            placeholder="Phương thức TT"
            clearable
            @update:model-value="store.setFilters({ paymentMethod: $event })"
          >
            <el-option label="Thẻ Mcoin" value="mcoin_card" />
            <el-option label="Visa/MasterCard" value="credit_card" />
            <el-option label="ATM nội địa" value="atm" />
            <el-option label="QRCode" value="qr_code" />
          </el-select>
        </div>
      </div>

      <div
        class="table-container flex-1 rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800"
      >
        <el-table
          :data="store.paginatedHistory"
          class="dark-table h-full"
          :header-cell-style="getTableHeaderStyle"
          :cell-style="getTableCellStyle"
          @sort-change="handleSortChange"
        >
          <el-table-column prop="id" label="ID" min-width="90" sortable />
          <el-table-column prop="transactionId" label="Mã GD" min-width="240" />

          <el-table-column prop="account" label="Tài khoản" min-width="160" />
          <el-table-column prop="paymentMethod" label="Phương thức TT" min-width="200">
            <template #default="{ row }">{{ getPaymentMethodText(row.paymentMethod) }}</template>
          </el-table-column>
          <el-table-column prop="amount" label="Mệnh giá" min-width="150" sortable>
            <template #default="{ row }">{{ formatCurrency(row.amount) }}</template>
          </el-table-column>
          <el-table-column prop="mcoin" label="Số Mcoin" min-width="125">
            <template #default="{ row }">{{ formatNumber(row.mcoin) }}</template>
          </el-table-column>

          <el-table-column prop="status" label="Trạng thái" min-width="150">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="timestamp" label="Thời gian" min-width="200" sortable>
            <template #default="{ row }">{{ formatDate(row.timestamp) }}</template>
          </el-table-column>
        </el-table>
      </div>

      <div class="mt-4 flex-shrink-0">
        <Pagination
          :pagination="paginationComputed"
          @page-change="handlePageChange"
          @per-page-change="handlePerPageChange"
        />
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import { useHistoryPaymentStore } from '@/state'
import Pagination from '@/components/common/Pagination.vue'
import { formatCurrency } from '@/utils/helpers/currency.helper'
import { formatDate } from '@/utils/helpers/datetime.helper'

const store = useHistoryPaymentStore()

const currentPageTitle = ref('Lịch sử Nạp Coin')

const handleSortChange = ({ prop, order }) => {
  store.setSort({ prop, order })
}
const formatNumber = (val) => new Intl.NumberFormat().format(val)

const getStatusType = (status) =>
  ({
    success: 'success',
    pending: 'warning',
    failed: 'danger',
    cancelled: 'info',
  })[status]
const getStatusText = (status) =>
  ({
    success: 'Thành công',
    pending: 'Chờ xử lý',
    failed: 'Thất bại',
    cancelled: 'Đã hủy',
  })[status]
const getPaymentMethodText = (method) =>
  ({
    mcoin_card: 'Thẻ Mcoin',
    credit_card: 'Visa/MasterCard',
    atm: 'ATM nội địa',
    qr_code: 'QRCode',
  })[method]

const paginationComputed = computed(() => ({
  current_page: store.pagination.current_page,
  per_page: store.pagination.per_page,
  total: store.pagination.total,
  from: store.pagination.from,
  to: store.pagination.to,
}))

const handlePageChange = (newPage) => {
  store.setPage(newPage)
}

const handlePerPageChange = (newSize) => {
  store.pagination.per_page = newSize
  store.setPage(1)
  store.fetchHistory()
}

const getTableHeaderStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color-page)',
    color: 'var(--el-text-color-primary)',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: '14px',
    padding: '16px 12px',
    borderBottom: '1px solid var(--el-border-color-light)',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
  }
}

const getTableCellStyle = () => {
  return {
    textAlign: 'center',
    fontSize: '14px',
    letterSpacing: '0.5px',
    height: '60px',
  }
}

onMounted(() => {
  store.fetchHistory()
})
</script>
