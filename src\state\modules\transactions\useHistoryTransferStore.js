import { defineStore } from 'pinia'

const generateDummyData = () => {
  const data = []
  const statuses = ['success', 'processing', 'failed']
  const games = ['JX1', 'JX2', 'VLTK Mobile']
  const servers = ['S1 - Thá<PERSON>ơn', 'S2 - Hoa Sơn', 'S3 - Trư<PERSON>ng <PERSON>']
  for (let i = 1; i <= 120; i++) {
    const mcoin = Math.floor(Math.random() * 1000) * 100
    const oldBalance = Math.floor(Math.random() * 1000000)
    data.push({
      id: i,
      transactionId: `TRF${Date.now() - Math.random() * 1000000}`,
      timestamp: new Date(Date.now() - Math.random() * 1000 * 3600 * 24 * 90).toLocaleString('vi-VN'),
      account: `account${Math.floor(Math.random() * 500)}`,
      game: games[i % games.length],
      server: servers[i % servers.length],
      character: `char_${Math.floor(Math.random() * 1000)}`,
      mcoin: mcoin,
      oldBalance: oldBalance,
      newBalance: oldBalance - mcoin,
      status: statuses[i % statuses.length],
    })
  }
  return data
}

export const useHistoryTransferStore = defineStore('historyTransfer', {
  state: () => ({
    history: [],
    loading: false,
    filters: {
      search: '',
      status: '',
    },
    sort: {
      prop: 'id',
      order: 'descending',
    },
    pagination: {
      current_page: 1,
      per_page: 15,
    },
  }),

  getters: {
    filteredHistory(state) {
      let data = [...state.history]

      if (state.filters.search) {
        const searchTerm = state.filters.search.toLowerCase()
        data = data.filter(
          (d) =>
            d.transactionId.toLowerCase().includes(searchTerm) ||
            d.account.toLowerCase().includes(searchTerm) ||
            d.character.toLowerCase().includes(searchTerm),
        )
      }

      if (state.filters.status) {
        data = data.filter((d) => d.status === state.filters.status)
      }

      data.sort((a, b) => {
        const aVal = a[state.sort.prop]
        const bVal = b[state.sort.prop]
        const modifier = state.sort.order === 'ascending' ? 1 : -1
        if (aVal < bVal) return -1 * modifier
        if (aVal > bVal) return 1 * modifier
        return 0
      })

      return data
    },

    paginatedHistory(state) {
      const start = (state.pagination.current_page - 1) * state.pagination.per_page
      const end = start + state.pagination.per_page
      return this.filteredHistory.slice(start, end)
    },

    paginationInfo(state) {
      const total = this.filteredHistory.length
      const start = (state.pagination.current_page - 1) * state.pagination.per_page
      return {
        total,
        from: total > 0 ? start + 1 : 0,
        to: Math.min(start + state.pagination.per_page, total),
      }
    },
  },

  actions: {
    async fetchHistory() {
      this.loading = true
      await new Promise((resolve) => setTimeout(resolve, 500))
      this.history = generateDummyData()
      this.loading = false
    },

    setFilters(newFilters) {
      this.filters = { ...this.filters, ...newFilters }
      this.pagination.current_page = 1
    },

    setSort(newSort) {
      this.sort = newSort
    },

    setPage(page) {
      this.pagination.current_page = page
    },
  },
})
