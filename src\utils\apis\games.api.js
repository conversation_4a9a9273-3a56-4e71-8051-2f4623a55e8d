import apiAxios from '@/utils/configs/axios.config.js'

// Helper function to clean empty parameters
const cleanParams = (params) => {
  const cleaned = {}
  Object.keys(params).forEach((key) => {
    // Keep 0 values but filter out empty strings, null, and undefined
    if (params[key] !== '' && params[key] !== null && params[key] !== undefined) {
      cleaned[key] = params[key]
    }
  })
  return cleaned
}

const gamesApi = {
  // Get games list with pagination and filters
  getGames(params = {}) {
    const cleanedParams = cleanParams(params)
    return apiAxios({
      method: 'get',
      url: 'games',
      params: cleanedParams,
    })
  },

  // Create new game
  createGame(gameData) {
    return apiAxios({
      method: 'post',
      url: 'games',
      data: gameData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // Update game
  updateGame(id, gameData) {
    let dataToSend
    if (gameData instanceof FormData) {
      dataToSend = gameData
    } else {
      dataToSend = new FormData()
      Object.entries(gameData).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          dataToSend.append(key, value)
        }
      })
    }

    return apiAxios({
      method: 'post', 
      url: `games/${id}`, 
      data: dataToSend,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // Delete game
  deleteGame(id) {
    return apiAxios({
      method: 'delete',
      url: `games/${id}`,
    })
  },

  // Update game status
  updateGameStatus(id, status) {
    return apiAxios({
      method: 'put',
      url: `games/${id}/status`,
      data: { status },
      headers: {
        'Content-Type': 'application/json',
      },
    })
  },
}

export default gamesApi
