<template>
  <section class="flex h-screen flex-col">
    <PageBreadcrumb :page-title="currentPageTitle" :breadcrumbs="[{ label: 'Trang chủ', to: '/' }]" />

    <div
      class="mb-4 flex flex-1 flex-col rounded-lg border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-600 dark:bg-gray-700/50"
    >
      <div class="mb-4 flex-shrink-0">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <el-input
            :model-value="store.filters.search"
            placeholder="Tìm mã GD, tài k<PERSON>n, nhân vật..."
            clearable
            @update:model-value="store.setFilters({ search: $event })"
          />

          <el-select
            :model-value="store.filters.status"
            placeholder="Trạng thái"
            clearable
            @update:model-value="store.setFilters({ status: $event })"
          >
            <el-option label="Thành công" value="success" />
            <el-option label="Đang xử lý" value="processing" />
            <el-option label="Thất bại" value="failed" />
          </el-select>
        </div>
      </div>

      <div class="min-h-0 flex-1 rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
        <el-table
          v-loading="store.loading"
          :data="store.paginatedHistory"
          class="dark-table h-full"
          :header-cell-style="getTableHeaderStyle"
          :cell-style="getTableCellStyle"
          @sort-change="handleSortChange"
        >
          <el-table-column prop="id" label="ID" min-width="90" sortable />
          <el-table-column prop="transactionId" label="Mã GD" min-width="240" />

          <el-table-column prop="account" label="Tài khoản" min-width="150" />
          <el-table-column prop="game" label="Game" min-width="120" />
          <el-table-column prop="server" label="Máy chủ" min-width="150" />
          <el-table-column prop="character" label="Nhân vật" min-width="130" />
          <el-table-column prop="mcoin" label="Số Mcoin" min-width="150" sortable>
            <template #default="{ row }">{{ formatNumber(row.mcoin) }}</template>
          </el-table-column>
          <el-table-column prop="oldBalance" label="Số dư cũ" min-width="150">
            <template #default="{ row }">{{ formatNumber(row.oldBalance) }}</template>
          </el-table-column>
          <el-table-column prop="newBalance" label="Số dư mới" min-width="150">
            <template #default="{ row }">{{ formatNumber(row.newBalance) }}</template>
          </el-table-column>
          <el-table-column prop="status" label="Trạng thái" min-width="150">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="timestamp" label="Thời gian" min-width="200" sortable>
            <template #default="{ row }">{{ formatDate(row.timestamp) }}</template>
          </el-table-column>
        </el-table>
      </div>

      <div class="mt-4 flex-shrink-0">
        <Pagination
          :pagination="paginationComputed"
          @page-change="handlePageChange"
          @per-page-change="handlePerPageChange"
        />
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import { useHistoryTransferStore } from '@/state'
import Pagination from '@/components/common/Pagination.vue'
import { formatDate } from '@/utils/helpers/datetime.helper'

const store = useHistoryTransferStore()

const currentPageTitle = ref('Lịch sử Chuyển Mcoin')

const handleSortChange = ({ prop, order }) => {
  store.setSort({ prop, order })
}

const formatNumber = (val) => new Intl.NumberFormat().format(val)

const getStatusType = (status) => ({ success: 'success', processing: 'warning', failed: 'danger' })[status]
const getStatusText = (status) => ({ success: 'Thành công', processing: 'Đang xử lý', failed: 'Thất bại' })[status]

const paginationComputed = computed(() => ({
  current_page: store.pagination.current_page,
  per_page: store.pagination.per_page,
  total: store.pagination.total,
  from: store.pagination.from,
  to: store.pagination.to,
}))

const handlePageChange = (newPage) => {
  store.setPage(newPage)
}

const handlePerPageChange = (newSize) => {
  store.pagination.per_page = newSize
  store.setPage(1)
  store.fetchHistory()
}

const getTableHeaderStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color-page)',
    color: 'var(--el-text-color-primary)',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: '14px',
    padding: '16px 12px',
    borderBottom: '1px solid var(--el-border-color-light)',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
  }
}

const getTableCellStyle = () => {
  return {
    textAlign: 'center',
    fontSize: '14px',
    letterSpacing: '0.5px',
    height: '60px',
  }
}

onMounted(() => {
  store.fetchHistory()
})
</script>
