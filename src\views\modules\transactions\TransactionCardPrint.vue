<template>
  <section class="flex h-screen flex-col">
    <PageBreadcrumb :page-title="currentPageTitle" :breadcrumbs="[{ label: 'Trang chủ', to: '/' }]" />

    <div
      class="mb-4 flex flex-1 flex-col rounded-lg border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-600 dark:bg-gray-700/50"
    >
      <div class="mb-4 flex-shrink-0">
        <div class="flex flex-col items-stretch gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:gap-2">
            <el-input
              v-model="filters.search"
              placeholder="Tìm kiếm serial, pin, user..."
              clearable
              @input="handleSearch"
              class="w-full sm:w-64"
            />
          </div>

          <ButtonCommon text="In Thẻ" type="primary" size="medium" :icon="PlusIcon" @click="handlePrintCard" />
        </div>
      </div>

      <div
        class="table-container flex-1 rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800"
      >
        <el-table
          :data="filteredCards"
          height="100%"
          class="dark-table h-full"
          :header-cell-style="getTableHeaderStyle"
          :cell-style="getTableCellStyle"
        >
          <el-table-column prop="id" label="ID" min-width="90" sortable />
          <el-table-column label="Serial / PIN" min-width="250">
            <template #default="{ row }">
              <div class="flex items-center justify-center gap-2">
                <span>{{ row.serial }}</span>
                <el-tooltip content="Copy serial" placement="right">
                  <el-icon class="cursor-pointer text-gray-500" @click="copyToClipboard(row.serial, 'Serial')">
                    <CopyDocument />
                  </el-icon>
                </el-tooltip>
              </div>
              <div class="mt-1 flex items-center justify-center gap-2">
                <span>{{ row.pin }}</span>
                <el-tooltip content="Copy pin" placement="right">
                  <el-icon class="cursor-pointer text-gray-500" @click="copyToClipboard(row.pin, 'PIN')">
                    <CopyDocument />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="created_by" label="User in thẻ" min-width="150" />
          <el-table-column prop="amount" label="Mệnh giá (VNĐ)" min-width="200" sortable>
            <template #default="{ row }">
              {{ formatCurrency(row.amount) }}
            </template>
          </el-table-column>
          <el-table-column prop="note" label="Ghi chú" min-width="200" />
          <el-table-column prop="status" label="Trạng thái" min-width="150">
            <template #default="{ row }">
              <el-tag
                :type="
                  {
                    '0': 'danger',
                    '1': 'success',
                    '2': 'warning',
                  }[row.status]
                "
              >
                {{ row.status_label }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="created_at" label="Thời gian in" min-width="180" sortable>
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="Thao tác" min-width="150">
            <template #default="{ row }">
              <el-tooltip :content="row.status !== 0 ? 'Thẻ đã được hủy' : 'Hủy thẻ'" placement="top">
                <ButtonCommon
                  :icon="TrashIcon"
                  type="danger"
                  size="small"
                  rounded
                  :disabled="row.status !== 0"
                  :class="
                    row.status !== 0 ? 'cursor-not-allowed border-none bg-gray-400 text-gray-700 hover:bg-gray-500' : ''
                  "
                  @click.stop="handleCancelCard(row.id)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="mt-4 flex-shrink-0">
        <Pagination :pagination="pagination" @page-change="handlePageChange" @per-page-change="handlePerPageChange" />
      </div>
    </div>

    <CardPrintModal v-model:visible="isModalVisible" @save="handleSuccess" />
  </section>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'

import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import { PlusIcon, TrashIcon } from '@/components/icons/index.js'
import CardPrintModal from '@/components/modules/transactions/CardPrintModal.vue'
import { useTransactions } from '@/composables/modules/transactions'
import Pagination from '@/components/common/Pagination.vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import { formatDate } from '@/utils/helpers/datetime.helper'
import { formatCurrency } from '@/utils/helpers/currency.helper'
import { ElMessageBox, ElMessage } from 'element-plus'
import { CopyDocument } from '@element-plus/icons-vue'

const { createCard, pagination, fetchCards, filterCards, cancelCard } = useTransactions()

const currentPageTitle = ref('Quản lý Thẻ Mcoin')
const isModalVisible = ref(false)
const filters = reactive({
  search: '',
})

const filteredCards = computed(() => {
  return filterCards(filters.search)
})

const getTableHeaderStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color-page)',
    color: 'var(--el-text-color-primary)',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: '14px',
    padding: '16px 12px',
    borderBottom: '1px solid var(--el-border-color-light)',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
  }
}

const getTableCellStyle = () => {
  return {
    textAlign: 'center',
    fontSize: '14px',
    letterSpacing: '0.5px',
  }
}

const handlePrintCard = () => {
  isModalVisible.value = true
}

const handleCancelCard = (cardId) => {
  ElMessageBox.confirm(`Hủy thẻ "${cardId}"? Hành động này không thể hoàn tác!`, 'Xác nhận', { type: 'warning' })
    .then(async () => {
      await cancelCard(cardId)
    })
    .catch(() => null)
}

const handlePageChange = (page) => {
  pagination.current_page = page
  fetchCards()
}

const handlePerPageChange = (perPage) => {
  pagination.per_page = perPage
  fetchCards()
}

const handleSuccess = async (cardData) => {
  try {
    await createCard(cardData)
    isModalVisible.value = false
  } catch (error) {
    console.error('Lỗi khi tạo thẻ:', error)
    ElMessage.error(error.response?.data?.message || 'Không thể tạo thẻ')
  }
}
function copyToClipboard(text, label) {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      ElMessage.success(`${label} đã được copy!`)
    })
    .catch(() => {
      ElMessage.error('Copy thất bại!')
    })
}

onMounted(() => {
  fetchCards()
})
</script>
