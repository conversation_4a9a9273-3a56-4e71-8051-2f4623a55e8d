import { defineStore } from 'pinia'
import { ref, computed, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Helper function to generate dummy data
const generateDummyCard = (id) => ({
  id,
  serial: `SABO${20240000 + id}`,
  pin: Math.random().toString(36).substring(2, 10).toUpperCase(),
  value: [10000, 20000, 50000, 100000, 200000, 500000][Math.floor(Math.random() * 6)],
  note: `Thẻ test ${id}`,
  status: Math.random() > 0.3 ? 'new' : 'used',
  createdAt: new Date(Date.now() - Math.random() * 1000 * 3600 * 24 * 30).toLocaleString('vi-VN'),
  printedBy: ['admin', 'sabo_user', 'manager'][Math.floor(Math.random() * 3)],
})

export const useTopupCardStore = defineStore('topupCard', () => {
  // State
  const allCards = ref([])
  const loading = ref(false)
  const filters = reactive({
    search: '',
    dateRange: null,
  })
  const sort = reactive({
    prop: 'id',
    order: 'descending',
  })

  // Getters (Computed)
  const filteredCards = computed(() => {
    let data = [...allCards.value]

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase()
      data = data.filter(
        (item) =>
          item.serial.toLowerCase().includes(searchTerm) ||
          item.pin.toLowerCase().includes(searchTerm) ||
          item.printedBy.toLowerCase().includes(searchTerm),
      )
    }

    if (filters.dateRange) {
      const [startDate, endDate] = filters.dateRange
      data = data.filter((item) => {
        const itemDate = new Date(item.createdAt.split(', ')[1].split('/').reverse().join('-'))
        return itemDate >= startDate && itemDate <= endDate
      })
    }

    if (sort.prop && sort.order) {
      data.sort((a, b) => {
        const aVal = a[sort.prop]
        const bVal = b[sort.prop]
        const modifier = sort.order === 'ascending' ? 1 : -1
        if (aVal < bVal) return -1 * modifier
        if (aVal > bVal) return 1 * modifier
        return 0
      })
    }

    return data
  })

  // Actions
  const fetchCards = () => {
    loading.value = true
    try {
      // Simulate API call
      const data = []
      for (let i = 1; i <= 50; i++) {
        data.push(generateDummyCard(i))
      }
      allCards.value = data
    } catch (error) {
      ElMessage.error('Lỗi khi tải dữ liệu thẻ.')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  const createCards = async (cardInfo) => {
    // Simulate API call
    return new Promise((resolve) => {
      setTimeout(() => {
        const newCards = []
        const lastId = allCards.value.length > 0 ? allCards.value.reduce((max, c) => Math.max(max, c.id), 0) : 0
        for (let i = 1; i <= cardInfo.quantity; i++) {
          const newCard = { ...generateDummyCard(lastId + i), ...cardInfo }
          newCards.push(newCard)
        }
        allCards.value.unshift(...newCards)
        ElMessage.success(`Tạo thành công ${cardInfo.quantity} thẻ!`)
        resolve(true)
      }, 500)
    })
  }

  const deleteCard = (cardId) => {
    ElMessageBox.confirm('Bạn có chắc chắn muốn xóa thẻ này?', 'Xác nhận xóa', {
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy',
      type: 'warning',
    })
      .then(() => {
        // Simulate API call
        const index = allCards.value.findIndex((c) => c.id === cardId)
        if (index !== -1) {
          allCards.value.splice(index, 1)
          ElMessage.success('Xóa thẻ thành công!')
        } else {
          ElMessage.error('Không tìm thấy thẻ để xóa.')
        }
      })
      .catch(() => {
        ElMessage.info('Đã hủy thao tác xóa.')
      })
  }

  const resetFilters = () => {
    filters.search = ''
    filters.dateRange = null
  }

  const handleSortChange = ({ prop, order }) => {
    sort.prop = prop
    sort.order = order
  }

  return {
    // State
    loading,
    filters,
    sort,
    // Getters
    filteredCards,
    // Actions
    fetchCards,
    createCards,
    deleteCard,
    resetFilters,
    handleSortChange,
  }
})
