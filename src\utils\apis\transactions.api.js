import apiAxios from '@/utils/configs/axios.config.js'

const transactionsApi = {
  createCard(cardData) {
    return apiAxios({
      method: 'post',
      url: 'transactions/card-prints',
      data: cardData,
    })
  },
  getCards(params = {}) {
    return apiAxios({
      method: 'get',
      url: 'transactions/card-prints',
      params,
    })
  },
  cancelCard(cardId) {
    return apiAxios({
      method: 'put',
      url: `transactions/card-prints/cancel/${cardId}`,
    })
  },
}

export default transactionsApi
