import apiAxios from '@/utils/configs/axios.config.js'

// Helper function to clean empty parameters
const cleanParams = (params) => {
  const cleaned = {}
  Object.keys(params).forEach((key) => {
    // Keep 0 values but filter out empty strings, null, and undefined
    if (params[key] !== '' && params[key] !== null && params[key] !== undefined) {
      cleaned[key] = params[key]
    }
  })
  return cleaned
}

const gamePackagesApi = {
  // Get game packages list with pagination and filters
  getGamePackages(params = {}) {
    const cleanedParams = cleanParams(params)
    return apiAxios({
      method: 'get',
      url: 'game-packages',
      params: cleanedParams,
    })
  },

  // Create new game package
  createGamePackage(packageData) {
    // Always use FormData for file uploads
    let formData
    if (packageData instanceof FormData) {
      formData = packageData
    } else {
      formData = new FormData()
      Object.entries(packageData).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          formData.append(key, value)
        }
      })
    }

    return apiAxios({
      method: 'post',
      url: 'game-packages',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // Update game package
  updateGamePackage(id, packageData) {
    // Always use FormData for file uploads
    let formData
    if (packageData instanceof FormData) {
      formData = packageData
    } else {
      formData = new FormData()
      Object.entries(packageData).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          formData.append(key, value)
        }
      })
    }

    return apiAxios({
      method: 'post',
      url: `game-packages/${id}`,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // Delete game package
  deleteGamePackage(id) {
    return apiAxios({
      method: 'delete',
      url: `game-packages/${id}`,
    })
  },

  // Update game package status
  updateGamePackageStatus(id, status) {
    return apiAxios({
      method: 'patch',
      url: `game-packages/${id}/status`,
      data: { status },
      headers: {
        'Content-Type': 'application/json',
      },
    })
  },
}

export default gamePackagesApi
