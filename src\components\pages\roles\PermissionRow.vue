<template>
  <tr
    class="permissions-row flex items-center justify-start overflow-x-auto border-b border-gray-200 px-2 py-4 last:border-b-0"
  >
    <th
      class="mr-2.5 w-[200px] shrink-0 text-start text-base font-medium break-words whitespace-normal text-gray-900 dark:text-white"
    >
      {{ group.name }}
    </th>

    <td>
      <div class="flex flex-wrap items-center gap-3.5">
        <div class="flex items-center gap-3.5" v-for="permission in group.permissions" :key="permission.id">
          <el-checkbox
            v-if="getPermissionByType(group, permission.type)"
            :label="checkboxLabel(permission)"
            :model-value="getPermissionActiveState(group, permission.type)"
            @change="handlePermissionChange(getPermissionByType(group, permission.type).id, $event)"
            size="large"
            :disabled="!props.canAssign || isCheckboxDisabled(getPermissionByType(group, permission.type)?.id)"
            :class="['permission-checkbox', { 'protected-role-checkbox': isRoleProtected }]"
          />
        </div>
      </div>
    </td>
  </tr>
</template>

<script setup>
const props = defineProps({
  group: {
    type: Object,
    required: true,
  },
  isRoleProtected: {
    type: Boolean,
    default: false,
  },
  canAssign: {
    type: Boolean,
    default: true,
  },
  getPermissionByType: {
    type: Function,
    required: true,
  },
  getPermissionActiveState: {
    type: Function,
    required: true,
  },
  isCheckboxDisabled: {
    type: Function,
    required: true,
  },
})

const emit = defineEmits(['permissionChange'])

const checkboxLabel = (permission) => {
  const baseLabel = props.getPermissionByType(props.group, permission.type)?.label
  return props.isRoleProtected ? `${baseLabel} - Không thể thay đổi quyền hạn` : baseLabel
}

const handlePermissionChange = (permissionId, value) => {
  console.log('check giá trị từ handlePermissionChange: ', value)
  emit('permissionChange', permissionId, value)
}
</script>
