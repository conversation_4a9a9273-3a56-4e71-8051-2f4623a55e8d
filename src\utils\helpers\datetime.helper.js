export function formatDate(dateString) {
  if (!dateString) return 'N/A'
  const date = new Date(dateString)

  const datePart = date.toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })

  const timePart = date.toLocaleTimeString('vi-VN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  })

  return `${datePart} ${timePart}`
}
