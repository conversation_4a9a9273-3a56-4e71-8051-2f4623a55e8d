import authApi from './auth.api.js'
import usersApi from './users.api.js'
import rolesApi from './roles.api.js'
import categoriesApi from './categories.api.js'
import postsApi from './posts.api.js'
import menusApi from './menus.api.js'
import settingsApi from './settings.api.js'
import uploadApi from './upload.api.js'
import staticPageApi from './static-page.api.js'
import siteSettingsApi from './siteSettings.api.js'
import overviewApi from './overview.api.js'
import transactionsApi from './transactions.api.js'
import gamesApi from './games.api.js'
import gamePackagesApi from './gamePackages.api.js'

export {
  authApi,
  usersApi,
  rolesApi,
  categoriesApi,
  postsApi,
  menusApi,
  settingsApi,
  uploadApi,
  staticPageApi,
  siteSettingsApi,
  overviewApi,
  transactionsApi,
  gamesApi,
  gamePackagesApi,
}
