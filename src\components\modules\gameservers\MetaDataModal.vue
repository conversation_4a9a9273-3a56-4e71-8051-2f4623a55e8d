<template>
  <Modal
    v-model="modalVisible"
    @close="handleClose"
    title="Chi tiết Meta Data Server"
    size="lg"
  >
    <div class="space-y-6">
      <!-- Server Info Header -->
      <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-blue-100 dark:bg-blue-800 rounded-lg flex items-center justify-center">
            <ServerIcon class="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">{{ serverName || 'Server' }}</h3>
            <p class="text-sm text-blue-700 dark:text-blue-300">Thông tin cấu hình và kết nối</p>
          </div>
        </div>
      </div>

      <!-- Meta Data Content -->
      <div class="space-y-4">
        <template v-if="metaData && Object.keys(metaData).length > 0">
          <!-- Connection Info -->
          <div v-if="connectionFields.length > 0" class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
              <LinkIcon class="w-4 h-4 text-gray-600 dark:text-gray-400" />
              Thông tin kết nối
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div v-for="field in connectionFields" :key="field.key" class="space-y-1">
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ field.label }}</label>
                <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded border dark:border-gray-600 text-sm">
                  <template v-if="field.type === 'password'">
                    <div class="flex items-center justify-between">
                      <span v-if="!showPasswords[field.key]" class="text-gray-900 dark:text-gray-100">{{ maskPassword(field.value) }}</span>
                      <span v-else class="font-mono text-gray-900 dark:text-gray-100">{{ field.value }}</span>
                      <button
                        @click="togglePassword(field.key)"
                        class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-xs"
                      >
                        {{ showPasswords[field.key] ? 'Ẩn' : 'Hiện' }}
                      </button>
                    </div>
                  </template>
                  <template v-else-if="field.type === 'url'">
                    <a :href="field.value" target="_blank" class="text-blue-600 dark:text-blue-400 hover:underline break-all">
                      {{ field.value }}
                    </a>
                  </template>
                  <template v-else>
                    <span class="break-all text-gray-900 dark:text-gray-100">{{ field.value }}</span>
                  </template>
                </div>
              </div>
            </div>
          </div>

          <!-- Configuration -->
          <div v-if="configFields.length > 0" class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
              <CogIcon class="w-4 h-4 text-gray-600 dark:text-gray-400" />
              Cấu hình
            </h4>
            <div class="space-y-3">
              <div v-for="field in configFields" :key="field.key" class="space-y-1">
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ field.label }}</label>
                <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded border dark:border-gray-600">
                  <template v-if="field.type === 'json'">
                    <pre class="text-xs whitespace-pre-wrap font-mono text-gray-800 dark:text-gray-200 max-h-40 overflow-y-auto">{{ formatJson(field.value) }}</pre>
                  </template>
                  <template v-else>
                    <span class="text-sm break-all text-gray-900 dark:text-gray-100">{{ field.value }}</span>
                  </template>
                </div>
              </div>
            </div>
          </div>

          <!-- Other Fields -->
          <div v-if="otherFields.length > 0" class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
              <DatabaseIcon class="w-4 h-4 text-gray-600 dark:text-gray-400" />
              Thông tin khác
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div v-for="field in otherFields" :key="field.key" class="space-y-1">
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ field.label }}</label>
                <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded border dark:border-gray-600 text-sm break-all text-gray-900 dark:text-gray-100">
                  {{ field.value }}
                </div>
              </div>
            </div>
          </div>

          <!-- Raw JSON View -->
          <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
                <CodeIcon class="w-4 h-4 text-gray-600 dark:text-gray-400" />
                Dữ liệu JSON gốc
              </h4>
              <ButtonCommon
                type="default"
                size="small"
                @click="copyToClipboard"
                :icon="CopyIcon"
              >
                Sao chép
              </ButtonCommon>
            </div>
            <div class="bg-gray-900 dark:bg-gray-950 text-green-400 dark:text-green-300 p-4 rounded-lg text-xs font-mono overflow-x-auto border dark:border-gray-700">
              <pre>{{ formattedJson }}</pre>
            </div>
          </div>
        </template>

        <!-- Empty State -->
        <template v-else>
          <div class="text-center py-12">
            <DatabaseIcon class="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Không có dữ liệu Meta Data</h3>
            <p class="text-gray-500 dark:text-gray-400">Server này chưa có thông tin cấu hình.</p>
          </div>
        </template>
      </div>
    </div>

    <template #footer>
      <div class="flex gap-3">
        <ButtonCommon type="default" @click="handleClose">
          Đóng
        </ButtonCommon>
        <ButtonCommon 
          v-if="metaData && Object.keys(metaData).length > 0"
          type="primary" 
          @click="handleEdit"
        >
          Chỉnh sửa
        </ButtonCommon>
      </div>
    </template>
  </Modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import Modal from '@/components/common/Modal.vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import { 
  ServerIcon, 
  LinkIcon, 
  CogIcon, 
  DatabaseIcon, 
  CodeIcon, 
  CopyIcon 
} from '@/components/icons/index.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  metaData: {
    type: Object,
    default: () => ({})
  },
  serverName: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'close', 'edit'])

// State
const showPasswords = ref({})

// Modal visibility computed property
const modalVisible = computed({
  get: () => {
    return props.modelValue
  },
  set: (value) => {
    emit('update:modelValue', value)
    if (!value) {
      emit('close')
    }
  }
})

// Computed
const formattedJson = computed(() => {
  return JSON.stringify(props.metaData, null, 2)
})

const connectionFields = computed(() => {
  const fields = []
  const data = props.metaData || {}
  
  if (data.url_center_server || data.url_center) {
    fields.push({
      key: 'url',
      label: 'URL Server',
      value: data.url_center_server || data.url_center,
      type: 'url'
    })
  }
  
  if (data.username) {
    fields.push({
      key: 'username',
      label: 'Tên đăng nhập',
      value: data.username,
      type: 'text'
    })
  }
  
  if (data.password) {
    fields.push({
      key: 'password',
      label: 'Mật khẩu',
      value: data.password,
      type: 'password'
    })
  }
  
  if (data.api_key) {
    fields.push({
      key: 'api_key',
      label: 'API Key',
      value: data.api_key,
      type: 'password'
    })
  }
  
  return fields
})

const configFields = computed(() => {
  const fields = []
  const data = props.metaData || {}
  
  if (data.custom_config) {
    fields.push({
      key: 'custom_config',
      label: 'Cấu hình tùy chỉnh',
      value: data.custom_config,
      type: 'json'
    })
  }
  
  if (data.timeout) {
    fields.push({
      key: 'timeout',
      label: 'Timeout (giây)',
      value: data.timeout,
      type: 'text'
    })
  }
  
  if (data.retries) {
    fields.push({
      key: 'retries',
      label: 'Số lần thử lại',
      value: data.retries,
      type: 'text'
    })
  }
  
  return fields
})

const otherFields = computed(() => {
  const fields = []
  const data = props.metaData || {}
  const knownKeys = new Set([
    'url_center_server', 'url_center', 'username', 'password', 
    'api_key', 'custom_config', 'timeout', 'retries'
  ])
  
  Object.keys(data).forEach(key => {
    if (!knownKeys.has(key) && data[key] !== null && data[key] !== undefined) {
      fields.push({
        key,
        label: formatFieldLabel(key),
        value: typeof data[key] === 'object' ? JSON.stringify(data[key]) : data[key],
        type: 'text'
      })
    }
  })
  
  return fields
})

// Watchers
watch(() => props.modelValue, (newShow) => {
  if (!newShow) {
    showPasswords.value = {}
  }
})

// Methods
const formatFieldLabel = (key) => {
  return key
    .replace(/_/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase())
}

const maskPassword = (password) => {
  return '*'.repeat(Math.min(password.length, 8))
}

const togglePassword = (key) => {
  showPasswords.value[key] = !showPasswords.value[key]
}

const formatJson = (value) => {
  if (typeof value === 'object') {
    return JSON.stringify(value, null, 2)
  }
  return value
}

const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(formattedJson.value)
    ElMessage.success('Đã sao chép vào clipboard')
  } catch (error) {
    ElMessage.error('Không thể sao chép')
  }
}

const handleClose = () => {
  emit('close')
}

const handleEdit = () => {
  emit('edit', props.metaData)
}
</script>

<style lang="scss" scoped>
pre {
  white-space: pre-wrap;
  word-break: break-word;
}

pre::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

pre::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dark pre::-webkit-scrollbar-track {
  background: #374151;
}

pre::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dark pre::-webkit-scrollbar-thumb {
  background: #6b7280;
}

pre::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dark pre::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
