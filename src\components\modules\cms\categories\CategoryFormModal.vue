<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? 'Chỉnh sửa <PERSON> mục' : '<PERSON><PERSON><PERSON> mục Mớ<PERSON>'"
    width="800px"
    :before-close="handleClose"
    :z-index="100000"
    append-to-body
    style="max-height: 90vh"
  >
    <div class="form-container" >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        label-position="top"
        hide-required-asterisk
      >
        <!-- Basic Information -->
        <div class="form-card">
          <div class="card-header">
            <h3 class="card-title">📋 Thông tin cơ bản</h3>
          </div>
          <div class="card-content">
            <el-form-item prop="name">
              <template #label>
                <span>Tên danh mục <span class="required-asterisk">*</span></span>
              </template>
              <el-input
                v-model="formData.name"
                placeholder="Nhập tên danh mục"
                maxlength="255"
                show-word-limit
                @input="(value) => { generateSlug(); nameHandlers.onInput(value); }"
                @blur="nameHandlers.onBlur"
              />
            </el-form-item>

            <el-form-item prop="slug">
              <template #label>
                <span>Slug <span class="required-asterisk">*</span></span>
              </template>
              <el-input 
                v-model="formData.slug" 
                placeholder="URL friendly name" 
                maxlength="255" 
                show-word-limit 
                @input="slugHandlers.onInput"
                @blur="slugHandlers.onBlur"
              />
              <div class="form-help">Để trống để tự động tạo từ tên danh mục</div>
            </el-form-item>

            <el-form-item label="Danh mục cha" prop="parent_id">
              <el-select v-model="formData.parent_id" placeholder="Chọn danh mục cha" clearable style="width: 100%">
                <el-option
                  v-for="option in parentOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="Trạng thái" prop="status">
              <el-select v-model="formData.status" placeholder="Chọn trạng thái" style="width: 100%">
                <el-option label="Đã xuất bản" :value="1" />
                <el-option label="Nháp" :value="0" />
              </el-select>
            </el-form-item>

            <el-form-item label="Mô tả" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="3"
                placeholder="Nhập mô tả danh mục"
                maxlength="500"
                show-word-limit
                @input="descriptionHandlers.onInput"
                @blur="descriptionHandlers.onBlur"
              />
            </el-form-item>
          </div>
        </div>

        <!-- Featured & Order Settings -->
        <div class="form-card">
          <div class="card-header">
            <h3 class="card-title">⚙️ Cài đặt hiển thị</h3>
          </div>
          <div class="card-content">
            <el-form-item>
              <template #label>
                <span>Danh mục nổi bật</span>
              </template>
              <el-switch v-model="formData.is_featured" />
            </el-form-item>

            <el-form-item label="Thứ tự nổi bật" prop="featured_order">
              <el-input-number
                v-model="formData.featured_order"
                :min="0"
                :max="999"
                style="width: 100%"
                :disabled="!formData.is_featured"
              />
            </el-form-item>

            <el-form-item label="Thứ tự hiển thị" prop="order">
              <el-input-number v-model="formData.order" :min="0" :max="999" style="width: 100%" />
            </el-form-item>
          </div>
        </div>

        <!-- SEO Settings -->
        <div class="form-card">
          <div class="card-header">
            <h3 class="card-title">🔍 Cài đặt SEO</h3>
          </div>
          <div class="card-content">
            <el-form-item label="Meta Title" prop="meta_title">
              <el-input 
                v-model="formData.meta_title" 
                placeholder="Tiêu đề SEO" 
                maxlength="60" 
                show-word-limit 
                @input="metaTitleHandlers.onInput"
                @blur="metaTitleHandlers.onBlur"
              />
            </el-form-item>

            <el-form-item label="Meta Description" prop="meta_description">
              <el-input
                v-model="formData.meta_description"
                type="textarea"
                :rows="2"
                placeholder="Mô tả SEO"
                maxlength="160"
                show-word-limit
                @input="metaDescriptionHandlers.onInput"
                @blur="metaDescriptionHandlers.onBlur"
              />
            </el-form-item>

            <el-form-item label="Meta Image" prop="meta_image">
              <div class="upload-container">
                <el-upload
                  :auto-upload="false"
                  :show-file-list="false"
                  accept="image/*"
                  drag
                  :before-upload="beforeUpload"
                  :on-change="handleFileChange"
                >
                  <div class="upload-dragger">
                    <div v-if="metaImagePreview" class="image-preview">
                      <img :src="metaImagePreview" alt="Meta Image" />
                      <div class="image-overlay">
                        <ButtonCommon type="primary" size="small" variant="text">Thay đổi ảnh</ButtonCommon>
                      </div>
                    </div>
                    <div v-else class="upload-placeholder">
                      <el-icon class="upload-icon"><Plus /></el-icon>
                      <div class="upload-text">
                        <p>Kéo thả ảnh vào đây hoặc <em>click để chọn</em></p>
                        <p class="upload-hint">Hỗ trợ: JPG, PNG, GIF (tối đa 2MB)</p>
                      </div>
                    </div>
                  </div>
                </el-upload>
                <ButtonCommon
                  v-if="metaImagePreview"
                  type="danger"
                  size="small"
                  variant="text"
                  @click="removeImage"
                  class="remove-image-btn"
                >
                  Xóa ảnh
                </ButtonCommon>
              </div>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <ButtonModalCommon 
        :loading="loading"
        :can-submit="true"
        cancel-text="Hủy"
        :submit-text="isEdit ? 'Cập nhật' : 'Thêm mới'"
        @cancel="handleCancel"
        @submit="handleSubmit"
      />
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useCategories } from '@/composables/modules/cms/useCategories.js'
import { useFormValidation, validationPresets } from '@/composables/useFormValidation.js'
import { generateSlugWithDash } from '@/utils/helpers/string.helper.js'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import '@/assets/styles/modules/cms/_category-form-modal.scss'
import ButtonModalCommon from '@/components/common/ButtonModalCommon.vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  category: {
    type: Object,
    default: null,
  },
  categories: {
    type: Array,
    default: () => [],
  },
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Composables
const { loading, createCategory, updateCategory, getCategoryOptions } = useCategories()
const { 
  formRef, 
  clearFieldValidation, 
  validateField, 
  validateForm,
  handleInputChange,
  handleFieldBlur,
  createFieldHandlers
} = useFormValidation()

// Form validation rules
const formRules = {
  name: [
    { required: true, message: 'Vui lòng nhập tên danh mục', trigger: 'blur' },
    { min: 1, max: 255, message: 'Tên danh mục phải từ 1-255 ký tự', trigger: 'blur' },
  ],
  slug: [
    { required: true, message: 'Vui lòng nhập slug', trigger: 'blur' },
    { min: 1, max: 255, message: 'Slug phải từ 1-255 ký tự', trigger: 'blur' },
    {
      pattern: /^[a-z0-9_-]+$/,
      message: 'Slug chỉ được chứa chữ thường, số, dấu gạch dưới và gạch ngang',
      trigger: 'blur',
    },
  ],
}

// Computed
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const isEdit = computed(() => !!props.category?.id)

const parentOptions = computed(() => {
  return getCategoryOptions(props.categories, props.category?.id)
})

// Form data - full API spec
const defaultFormData = {
  name: '',
  slug: '',
  parent_id: null,
  description: '',
  status: 0,
  is_featured: false,
  featured_order: 0,
  order: 0,
  meta_title: '',
  meta_description: '',
  meta_image: '',
}

const formData = ref({ ...defaultFormData })

// Upload related data
const metaImageFile = ref(null)
const metaImagePreview = ref('')

// Methods
const generateSlug = () => {
  const newSlug = generateSlugWithDash(formData.value.name)
  formData.value.slug = newSlug
  
  // Clear validation for the slug field after auto-generation
  nextTick(() => {
    setTimeout(() => {
      if (formRef.value) {
        formRef.value.clearValidate('slug')
      }
    }, 100)
  })
}

// Create input handlers for real-time validation
const nameHandlers = createFieldHandlers('name', validationPresets.name)
const slugHandlers = createFieldHandlers('slug', validationPresets.slug)
const descriptionHandlers = createFieldHandlers('description', validationPresets.description)
const metaTitleHandlers = createFieldHandlers('meta_title', { required: false, minLength: 0 })
const metaDescriptionHandlers = createFieldHandlers('meta_description', { required: false, minLength: 0 })

const resetForm = () => {
  formData.value = { ...defaultFormData }
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const populateForm = (category) => {
  if (category) {
    formData.value = {
      name: category?.name || '',
      slug: category?.slug || '',
      parent_id: category?.parent_id || null,
      description: category?.description || '',
      status: category?.status !== undefined ? category?.status : 0,
      is_featured: Boolean(category?.is_featured),
      featured_order: category?.featured_order || 0,
      order: category?.order || 0,
      meta_title: category?.meta_title || '',
      meta_description: category?.meta_description || '',
      meta_image: category?.meta_image || '',
    }
  } else {
    resetForm()
  }
}

const handleSubmit = async () => {
  try {
    const isValid = await validateForm()
    if (!isValid) return

    // Tạo FormData để gửi lên server
    const form = new FormData()
    form.append('name', formData.value.name)
    form.append('slug', formData.value.slug)
    form.append('description', formData.value.description || '')
    form.append('parent_id', formData.value.parent_id || '')
    form.append('status', formData.value.status)
    form.append('is_featured', formData.value.is_featured ? '1' : '0')
    form.append('featured_order', formData.value.featured_order)
    form.append('order', formData.value.order)
    form.append('meta_title', formData.value.meta_title || '')
    form.append('meta_description', formData.value.meta_description || '')
    // Chỉ append nếu là file thực sự
    if (metaImageFile.value && metaImageFile.value instanceof File) {
      form.append('meta_image', metaImageFile.value)
    }

    if (isEdit.value) {
      await updateCategory(props.category.id, form)
    } else {
      await createCategory(form)
    }

    emit('success')
  } catch (error) {
    console.error('Form validation failed:', error)
  }
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleCancel = () => {
  emit('update:visible', false)
}

// Upload handlers
const beforeUpload = (file) => {
  const isValidType = ['image/jpeg', 'image/png', 'image/gif'].includes(file.type)
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isValidType) {
    ElMessage.error('Chỉ hỗ trợ file JPG, PNG, GIF!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('Kích thước file không được vượt quá 2MB!')
    return false
  }
  return true
}

const handleFileChange = (file) => {
  if (!beforeUpload(file.raw)) return false
  metaImageFile.value = file.raw // file.raw là File object
  metaImagePreview.value = URL.createObjectURL(file.raw)
  // Không set formData.value.meta_image nữa, chỉ dùng cho preview
}

const removeImage = () => {
  metaImageFile.value = null
  metaImagePreview.value = ''
  formData.value.meta_image = ''
}

// Watch for category changes
watch(
  () => props.category,
  (newCategory) => {
    populateForm(newCategory)
    if (newCategory && newCategory.meta_image) {
      metaImagePreview.value = newCategory.meta_image
      metaImageFile.value = null
    } else {
      metaImagePreview.value = ''
      metaImageFile.value = null
    }
  },
  { immediate: true },
)

// Watch for dialog visibility
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      populateForm(props.category)
    }
  },
)
</script>


