<template>
  <div class="mcoin-promotion-campaign-management-wrapper">
    <PageBreadcrumb 
      :page-title="currentPageTitle" 
      :breadcrumbs="[{ label: 'Quản lý Game Server', to: '/gameservers' }]" 
    />

    <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-800 dark:bg-white/[0.03]">
      <!-- Filters -->
      <div class="mb-6 rounded-lg border border-gray-200 bg-gray-50 p-6 dark:border-gray-700 dark:bg-gray-900/50">
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <el-input
              v-model="searchTerm"
              placeholder="Tìm kiếm theo tên chiến dịch..."
              :prefix-icon="SearchIcon"
              @clear="handleResetFilters"
              clearable
              style="width: 300px"
              size="large"
            />
          </div>
          <div class="flex items-center gap-2">
            <el-select
              v-model="selectedStatus"
              placeholder="Lọ<PERSON> theo trạng thái"
              @change="handleStatusFilter"
              clearable
              style="width: 200px"
              size="large"
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
          <div class="ml-auto flex items-center gap-3">
            <ButtonCommon type="primary" @click="openAddModal" :icon="PlusIcon">
              Thêm chiến dịch
            </ButtonCommon>
          </div>
        </div>
      </div>

      <!-- Promotion Campaigns Table -->
      <div
        class="table-container overflow-hidden rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800"
        style="overflow-x: auto;"
      >
        <el-table
          v-loading="loading"
          :data="paginatedCampaigns"
          row-key="id"
          style="width: 100%"
          :header-cell-style="getTableHeaderStyle"
          :cell-style="getTableCellStyle"
          table-layout="auto"
          class="promotion-campaign-table"
          :row-class-name="getRowClassName"
          empty-text="Chưa có chiến dịch nào"
        >
          <!-- STT Column -->
          <el-table-column label="STT" width="80" align="center">
            <template #default="{ $index }">
              <span class="text-sm text-gray-600">{{ (currentPage - 1) * pageSize + $index + 1 }}</span>
            </template>
          </el-table-column>

          <!-- Name & Description Combined Column -->
          <el-table-column label="Tên chiến dịch" min-width="200" align="left">
            <template #default="{ row }">
              <div class="px-2 py-1">
                <el-tooltip 
                  :content="row.description" 
                  placement="top"
                  :disabled="row.description.length <= 50"
                  effect="dark"
                >
                  <div class="campaign-info-container cursor-help">
                    <div class="font-semibold text-gray-900 dark:text-white line-clamp-1 mb-1 campaign-name">
                      {{ row.name }}
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 line-clamp-2 campaign-description">
                      {{ row.description }}
                    </div>
                  </div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>

          <!-- Duration Column -->
          <el-table-column label="Thời gian diễn ra" min-width="200" align="center">
            <template #default="{ row }">
              <div class="text-center text-sm">
                <div class="text-gray-900 dark:text-white">{{ formatDateTime(row.start_date) }}</div>
                <div class="text-gray-500 text-xs my-1">→</div>
                <div class="text-gray-900 dark:text-white">{{ formatDateTime(row.end_date) }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- Bonus Column -->
          <el-table-column label="Ưu đãi" width="120" align="center">
            <template #default="{ row }">
              <div class="flex justify-center">
                <el-tag 
                  :type="getBonusTagType(row.bonus_type)" 
                  size="large"
                  class="font-medium"
                >
                  {{ formatBonus(row.bonus_type, row.bonus_value) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <!-- Status Column -->
          <el-table-column label="Trạng thái" width="100" align="center">
            <template #default="{ row }">
              <div class="flex justify-center">
                <el-tag 
                  :type="getStatusTagType(row.status)" 
                  size="large"
                  class="font-medium"
                >
                  {{ getStatusLabel(row.status) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <!-- Actions Column -->
          <el-table-column label="HÀNH ĐỘNG" width="250" fixed="right" align="center">
            <template #default="{ row }">
              <div class="flex items-center justify-center gap-2 flex-wrap px-2">
                <!-- Status control buttons with icons and tooltips -->
                <ButtonCommon
                  v-if="row.status === 'paused'"
                  type="success"
                  size="small"
                  :icon="PlayIcon"
                  :loading="statusLoading[row.id]"
                  tooltip="Bật chiến dịch"
                  @click="handleStatusChange(row, 'active')"
                />
                
                <ButtonCommon
                  v-if="row.status === 'active'"
                  type="warning"
                  size="small"
                  :icon="PauseIcon"
                  :loading="statusLoading[row.id]"
                  tooltip="Tạm dừng chiến dịch"
                  @click="handleStatusChange(row, 'paused')"
                />
                
                <ButtonCommon
                  v-if="row.status === 'active'"
                  type="danger"
                  size="small"
                  :icon="StopIcon"
                  :loading="statusLoading[row.id]"
                  tooltip="Kết thúc chiến dịch"
                  @click="handleStatusChange(row, 'ended')"
                />

                <!-- Edit and Delete buttons with icon only -->
                <ButtonCommon
                  v-if="row.status !== 'ended'"
                  type="warning"
                  size="small"
                  :icon="EditIcon"
                  tooltip="Sửa chiến dịch"
                  @click="openEditModal(row)"
                />
                
                <ButtonCommon
                  type="danger"
                  size="small"
                  :icon="TrashIcon"
                  tooltip="Xóa chiến dịch"
                  @click="confirmDelete(row)"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- Pagination -->
      <Pagination :pagination="pagination" @page-change="handlePageChange" @per-page-change="handlePerPageChange" />
    </div>

    <!-- Promotion Campaign Modal -->
    <McoinPromotionCampaignModal
      v-model="showModal"
      :editing-campaign="editingCampaign"
      :campaigns="campaigns"
      @close="closeModal"
      @save="handleSaveCampaign"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import Pagination from '@/components/common/Pagination.vue'
import McoinPromotionCampaignModal from '@/components/modules/gameservers/McoinPromotionCampaignModal.vue'
import { PlusIcon, PlayIcon, PauseIcon, StopIcon, EditIcon, TrashIcon } from '@/components/icons/index.js'

// Icons
const SearchIcon = Search

// Page title
const currentPageTitle = ref('Quản lý chiến dịch khuyến mại Mcoin')

// State
const loading = ref(false)
const campaigns = ref([])
const showModal = ref(false)
const editingCampaign = ref(null)
const statusLoading = ref({})

// Filter states
const searchTerm = ref('')
const selectedStatus = ref('')

// Pagination
const currentPage = ref(1)
const pageSize = ref(10)
const pagination = computed(() => ({
  current_page: currentPage.value,
  per_page: pageSize.value,
  total: filteredCampaigns.value.length,
  from: filteredCampaigns.value.length === 0 ? 0 : (currentPage.value - 1) * pageSize.value + 1,
  to: Math.min(currentPage.value * pageSize.value, filteredCampaigns.value.length),
}))

// Status options
const statusOptions = [
  { label: 'Đang hoạt động', value: 'active' },
  { label: 'Tạm dừng', value: 'paused' },
  { label: 'Đã kết thúc', value: 'ended' }
]

// Sample data
const sampleCampaigns = [
  {
    id: 1,
    name: 'Giáng sinh 2025',
    description: 'Ưu đãi nạp xu được tặng thêm 20% trong dịp Giáng Sinh',
    start_date: '2025-12-20 00:00:00',
    end_date: '2025-12-31 23:59:59',
    bonus_type: 1, // 1: %, 2: Xu, 3: Item
    bonus_value: 20,
    status: 'active'
  },
  {
    id: 2,
    name: 'Tết nguyên đán 2026',
    description: 'Ưu đãi nạp xu được tặng thêm 100 xu trong dịp Tết',
    start_date: '2026-01-20 00:00:00',
    end_date: '2026-02-10 23:59:59',
    bonus_type: 2,
    bonus_value: 100,
    status: 'paused'
  },
  {
    id: 3,
    name: 'Khuyến mại Item đặc biệt',
    description: 'Tặng item đặc biệt khi nạp xu',
    start_date: '2025-08-01 00:00:00',
    end_date: '2025-08-20 23:59:59',
    bonus_type: 3,
    bonus_value: 12345,
    status: 'ended'
  }
]

// Computed
const filteredCampaigns = computed(() => {
  let filtered = campaigns.value

  // Filter by search term
  if (searchTerm.value.trim()) {
    const search = searchTerm.value.toLowerCase().trim()
    filtered = filtered.filter(campaign =>
      campaign.name.toLowerCase().includes(search) ||
      campaign.description.toLowerCase().includes(search)
    )
  }

  // Filter by status
  if (selectedStatus.value) {
    filtered = filtered.filter(campaign => campaign.status === selectedStatus.value)
  }

  return filtered
})

const paginatedCampaigns = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredCampaigns.value.slice(start, end)
})

// Helper functions
const formatDateTime = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

const formatBonus = (bonusType, bonusValue) => {
  switch (bonusType) {
    case 1:
      return `+${bonusValue}%`
    case 2:
      return `+${bonusValue} xu`
    case 3:
      return `Item ID: ${bonusValue}`
    default:
      return 'N/A'
  }
}

const getBonusTagType = (bonusType) => {
  switch (bonusType) {
    case 1:
      return 'success'
    case 2:
      return 'warning'
    case 3:
      return 'info'
    default:
      return ''
  }
}

const getStatusLabel = (status) => {
  switch (status) {
    case 'active':
      return 'Đang hoạt động'
    case 'paused':
      return 'Tạm dừng'
    case 'ended':
      return 'Đã kết thúc'
    default:
      return 'N/A'
  }
}

const getStatusTagType = (status) => {
  switch (status) {
    case 'active':
      return 'success'
    case 'paused':
      return 'warning'
    case 'ended':
      return 'danger'
    default:
      return ''
  }
}

// Enhanced table styles
const getTableHeaderStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color-page)',
    color: 'var(--el-text-color-primary)',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: '14px',
    padding: '16px 12px',
    borderBottom: '1px solid var(--el-border-color-light)',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
  }
}

const getTableCellStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color)',
    color: 'var(--el-text-color-primary)',
    borderBottom: '1px solid var(--el-border-color-lighter)',
    padding: '16px 12px',
    fontSize: '14px',
  }
}

const getRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// Methods
const loadCampaigns = async () => {
  loading.value = true
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1000))
  campaigns.value = sampleCampaigns
  loading.value = false
}

const handleStatusFilter = async () => {
  currentPage.value = 1 // Reset to first page when filtering
}

const handleResetFilters = async () => {
  searchTerm.value = ''
  selectedStatus.value = ''
  currentPage.value = 1
}

const handlePageChange = (page) => {
  currentPage.value = page
}

const handlePerPageChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleStatusChange = async (campaign, newStatus) => {
  try {
    // Set loading state for this specific campaign
    statusLoading.value[campaign.id] = true

    // Special rule: Only one campaign can be active at a time
    if (newStatus === 'active') {
      const activeCampaign = campaigns.value.find(c => c.status === 'active' && c.id !== campaign.id)
      if (activeCampaign) {
        await ElMessageBox.confirm(
          `Hiện tại chiến dịch "${activeCampaign.name}" đang hoạt động. Bạn có muốn dừng chiến dịch đó và bật chiến dịch "${campaign.name}" không?`,
          'Xác nhận thay đổi',
          {
            confirmButtonText: 'Đồng ý',
            cancelButtonText: 'Hủy',
            type: 'warning',
          }
        )
        
        // Pause the currently active campaign
        const activeIndex = campaigns.value.findIndex(c => c.id === activeCampaign.id)
        if (activeIndex !== -1) {
          campaigns.value[activeIndex].status = 'paused'
        }
      }
    }

    // Confirm status change for critical actions
    if (newStatus === 'ended') {
      await ElMessageBox.confirm(
        `Bạn có chắc chắn muốn kết thúc chiến dịch "${campaign.name}"? Hành động này không thể hoàn tác.`,
        'Xác nhận kết thúc chiến dịch',
        {
          confirmButtonText: 'Kết thúc',
          cancelButtonText: 'Hủy',
          type: 'warning',
        }
      )
    }

    // Update campaign status
    const campaignIndex = campaigns.value.findIndex(c => c.id === campaign.id)
    if (campaignIndex !== -1) {
      campaigns.value[campaignIndex].status = newStatus
      
      const statusMessages = {
        'active': 'Đã bật chiến dịch',
        'paused': 'Đã tạm dừng chiến dịch',
        'ended': 'Đã kết thúc chiến dịch'
      }
      
      ElMessage.success(`${statusMessages[newStatus]} "${campaign.name}"`)
    }
  } catch (error) {
    // User cancelled or error occurred
    if (error !== 'cancel') {
      ElMessage.error('Có lỗi xảy ra khi cập nhật trạng thái')
    }
  } finally {
    // Remove loading state
    statusLoading.value[campaign.id] = false
  }
}

const openAddModal = () => {
  editingCampaign.value = null
  showModal.value = true
}

const openEditModal = (campaign) => {
  editingCampaign.value = campaign
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  editingCampaign.value = null
}

const handleSaveCampaign = (campaignData) => {
  try {
    if (editingCampaign.value) {
      // Update existing campaign
      const index = campaigns.value.findIndex(c => c.id === editingCampaign.value.id)
      if (index !== -1) {
        campaigns.value[index] = {
          ...campaigns.value[index],
          ...campaignData
        }
        ElMessage.success('Cập nhật chiến dịch thành công!')
      }
    } else {
      // Add new campaign
      const newCampaign = {
        id: Date.now(),
        ...campaignData
      }
      campaigns.value.push(newCampaign)
      ElMessage.success('Thêm chiến dịch mới thành công!')
    }

    closeModal()
  } catch (error) {
    ElMessage.error('Có lỗi xảy ra khi lưu dữ liệu')
  }
}

const confirmDelete = async (campaign) => {
  try {
    await ElMessageBox.confirm(
      `Bạn có chắc chắn muốn xóa chiến dịch "${campaign.name}"?`,
      'Xác nhận xóa',
      {
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
        type: 'warning',
      }
    )
    
    const index = campaigns.value.findIndex(c => c.id === campaign.id)
    if (index !== -1) {
      campaigns.value.splice(index, 1)
      ElMessage.success('Xóa chiến dịch thành công!')
    }
  } catch {
    // User cancelled
  }
}

// Auto search when input changes
let searchTimeout = null
watch(searchTerm, (newValue) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  searchTimeout = setTimeout(() => {
    currentPage.value = 1 // Reset to first page when searching
  }, 300)
})

// Lifecycle
onMounted(() => {
  loadCampaigns()
})
</script>

<style lang="scss" scoped>
/* Enhanced table styling */
:deep(.promotion-campaign-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.promotion-campaign-table .even-row) {
  background-color: var(--el-bg-color);
}

:deep(.promotion-campaign-table .odd-row) {
  background-color: var(--el-fill-color-light);
}

:deep(.promotion-campaign-table .even-row:hover),
:deep(.promotion-campaign-table .odd-row:hover) {
  background-color: var(--el-fill-color) !important;
}

:deep(.promotion-campaign-table .el-table__header-wrapper) {
  background-color: var(--el-bg-color-page);
}

:deep(.promotion-campaign-table .el-table__row) {
  transition: background-color 0.2s ease;
  height: 66px !important;
  min-height: 66px !important;
}

:deep(.promotion-campaign-table .el-table__cell) {
  border-color: var(--el-border-color-lighter);
  vertical-align: middle;
}

/* Ensure all table headers have consistent styling */
:deep(.promotion-campaign-table .el-table__header .el-table__cell) {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  font-size: 14px !important;
  padding: 12px !important;
  border-bottom: 1px solid var(--el-border-color-light) !important;
  text-align: center !important;
  height: 48px !important;
}

/* Ensure all table body cells have consistent styling */
:deep(.promotion-campaign-table .el-table__body .el-table__cell) {
  background-color: var(--el-bg-color) !important;
  color: var(--el-text-color-primary) !important;
  border-bottom: 1px solid var(--el-border-color-lighter) !important;
  padding: 12px !important;
  font-size: 14px !important;
  height: 66px !important;
  vertical-align: middle !important;
}

/* Table empty state styling */
:deep(.promotion-campaign-table .el-table__empty-block) {
  background-color: var(--el-bg-color);
  border: none;
  padding: 40px 20px;
}

:deep(.promotion-campaign-table .el-table__empty-text) {
  color: var(--el-text-color-regular);
  font-size: 14px;
  font-weight: 500;
}

/* Dark mode support */
.dark :deep(.promotion-campaign-table .el-table__empty-block) {
  background-color: #1e293b;
}

.dark :deep(.promotion-campaign-table .el-table__empty-text) {
  color: #94a3b8;
}

/* Responsive improvements */
:deep(.promotion-campaign-table .el-table__body-wrapper) {
  overflow-x: auto;
}

:deep(.promotion-campaign-table .el-table__fixed-right) {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

/* Text truncation utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

/* Campaign info container styling */
.campaign-info-container {
  transition: all 0.2s ease;
  padding: 4px 0;
}

.campaign-info-container:hover {
  transform: translateY(-1px);
}

/* Campaign name styling - highlighted */
.campaign-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-color-primary);
  transition: color 0.2s ease;
  line-height: 1.3;
}

.campaign-name:hover {
  color: var(--el-color-primary-light-3);
}

/* Campaign description styling */
.campaign-description {
  font-size: 11px;
  line-height: 1.4;
  opacity: 0.8;
  margin-top: 2px;
}

/* Dark mode support for campaign info */
.dark .campaign-name {
  color: #73b4ff;
}

.dark .campaign-name:hover {
  color: #a8c5ff;
}

.dark .campaign-description {
  color: #94a3b8;
}

/* Cursor pointer for tooltips */
.cursor-help {
  cursor: pointer;
}

.cursor-help:hover {
  text-decoration: underline;
  text-decoration-style: dotted;
}

/* Action buttons spacing and layout */
:deep(.promotion-campaign-table .el-button + .el-button) {
  margin-left: 4px;
}

:deep(.promotion-campaign-table .el-button) {
  margin: 2px;
  min-width: 32px;
  width: 32px;
  height: 32px;
  padding: 0 !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

:deep(.promotion-campaign-table .el-button svg) {
  width: 16px !important;
  height: 16px !important;
  margin: 0 !important;
}

/* Status tag styling */
:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
}
</style>
