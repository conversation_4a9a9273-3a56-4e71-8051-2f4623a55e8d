<template>
  <el-dialog
    :model-value="visible"
    title="Thêm mới thẻ Mcoin"
    width="500px"
    :before-close="handleClose"
    :z-index="100000"
    append-to-body
    @update:model-value="$emit('update:visible', $event)"
  >
    <el-form
      class="p-6"
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
      label-width="120px"
      hide-required-asterisk
    >
      <el-form-item prop="amount">
        <template #label>
          <span>Mệnh giá <span class="required-asterisk">*</span></span>
        </template>
        <el-select v-model="form.amount" placeholder="Chọn mệnh giá" class="w-full">
          <el-option v-for="option in amountOptions" :key="option.value" :label="option.label" :value="option.value" />
        </el-select>
        <div class="form-help">Mệnh giá của thẻ</div>
      </el-form-item>
      <el-form-item prop="quantity">
        <template #label>
          <span>S<PERSON> lượng <span class="required-asterisk">*</span></span>
        </template>
        <el-input-number v-model="form.quantity" :min="1" :max="1000" class="w-full" />
        <div class="form-help">Số lượng thẻ cần in</div>
      </el-form-item>
      <el-form-item label="Ghi chú" prop="note">
        <el-input v-model="form.note" type="textarea" :rows="3" placeholder="Nhập ghi chú" />
        <div class="form-help">Ghi chú thêm</div>
      </el-form-item>
    </el-form>
    <ButtonModalCommon
      :can-submit="true"
      cancel-text="Hủy"
      submit-text="Thêm mới"
      @cancel="handleClose"
      @submit="handleSave"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive } from 'vue'
import ButtonModalCommon from '@/components/common/ButtonModalCommon.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})

const formRef = ref(null)
const form = reactive({
  amount: '',
  quantity: 1,
  note: '',
})
const amountOptions = ref([
  { value: 10000, label: '10,000 VND' },
  { value: 20000, label: '20,000 VND' },
  { value: 50000, label: '50,000 VND' },
  { value: 100000, label: '100,000 VND' },
  { value: 200000, label: '200,000 VND' },
  { value: 500000, label: '500,000 VND' },
])
const rules = {
  amount: [{ required: true, message: 'Vui lòng chọn mệnh giá', trigger: 'change' }],
}
const emit = defineEmits(['update:visible', 'save'])

const handleClose = () => {
  resetForm()
  emit('update:visible', false)
}

const handleSave = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      emit('save', { ...form })
    }
  })
}

const resetForm = () => {
  form.amount = ''
  form.quantity = 1
  form.note = ''
}
</script>

<style scoped>
.w-full {
  width: 100% !important;
}
</style>
