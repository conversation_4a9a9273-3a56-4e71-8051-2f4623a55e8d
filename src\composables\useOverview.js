/**
 * Overview Management Composable
 * Handles overview data fetching
 */

import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { overviewApi } from '@/utils/apis/index.js'
import { extractApiResponse } from '@/utils/helpers/response.helper.js'

// Global message tracking to prevent spam
const messageHistory = new Map()
const MESSAGE_COOLDOWN = 2000 // 2 seconds

const preventMessageSpam = (message) => {
  const now = Date.now()
  const lastTime = messageHistory.get(message) || 0

  if (now - lastTime > MESSAGE_COOLDOWN) {
    messageHistory.set(message, now)
    return true
  }
  return false
}

export function useOverview() {
  // State
  const loading = ref(false)
  const overviewData = ref({})

  // Message helper with spam prevention
  const showMessage = (type, message) => {
    if (preventMessageSpam(message)) {
      ElMessage[type](message)
    } else {
    }
  }

  // Get overview data
  const fetchOverview = async () => {
    try {
      loading.value = true
      
      const response = await overviewApi.getOverview()
     
      
      // Sử dụng helper để tr<PERSON>h truy cập nhiều cấp
      const { success, data, message } = extractApiResponse(response)
      
      if (success) {
        overviewData.value = data || {}
        return {
          success: true,
          data,
          message
        }
      } else {
        showMessage('error', message || 'Lỗi khi tải dữ liệu tổng quan')
        return {
          success: false,
          data: null,
          message
        }
      }
    } catch (error) {
      console.error('Error fetching overview:', error)
      const errorMessage = error.message || 'Lỗi khi tải dữ liệu tổng quan'
      showMessage('error', errorMessage)
      return {
        success: false,
        data: null,
        message: errorMessage
      }
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    loading,
    overviewData,

    // API methods
    fetchOverview,
  }
}
