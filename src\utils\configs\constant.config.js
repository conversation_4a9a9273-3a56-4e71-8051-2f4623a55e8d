/*
  Application Constants
  <PERSON><PERSON><PERSON> hằng số được sử dụng trong toàn bộ ứng dụng
*/

// User Status Constants
export const USER_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  SUSPENDED: 2,
}

export const USER_STATUS_LABELS = {
  [USER_STATUS.ACTIVE]: 'Hoạt động',
  [USER_STATUS.INACTIVE]: 'Không hoạt động',
  [USER_STATUS.SUSPENDED]: 'Cấm',
}

export const USER_STATUS_OPTIONS = [
  { value: USER_STATUS.ACTIVE, label: USER_STATUS_LABELS[USER_STATUS.ACTIVE] },
  { value: USER_STATUS.INACTIVE, label: USER_STATUS_LABELS[USER_STATUS.INACTIVE] },
  { value: USER_STATUS.SUSPENDED, label: USER_STATUS_LABELS[USER_STATUS.SUSPENDED] },
]

// Game Status Constants
export const GAME_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
}

export const GAME_STATUS_LABELS = {
  [GAME_STATUS.ACTIVE]: 'Hoạt động',
  [GAME_STATUS.INACTIVE]: 'Không hoạt động',
}

export const GAME_STATUS_OPTIONS = [
  { value: GAME_STATUS.ACTIVE, label: GAME_STATUS_LABELS[GAME_STATUS.ACTIVE] },
  { value: GAME_STATUS.INACTIVE, label: GAME_STATUS_LABELS[GAME_STATUS.INACTIVE] },
]

// Server Status Constants
export const SERVER_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  MAINTENANCE: 3,
}

export const SERVER_STATUS_LABELS = {
  [SERVER_STATUS.ACTIVE]: 'Hoạt động',
  [SERVER_STATUS.INACTIVE]: 'Không hoạt động',
  [SERVER_STATUS.MAINTENANCE]: 'Bảo trì',
}

export const SERVER_STATUS_OPTIONS = [
  { value: SERVER_STATUS.ACTIVE, label: SERVER_STATUS_LABELS[SERVER_STATUS.ACTIVE] },
  { value: SERVER_STATUS.INACTIVE, label: SERVER_STATUS_LABELS[SERVER_STATUS.INACTIVE] },
  { value: SERVER_STATUS.MAINTENANCE, label: SERVER_STATUS_LABELS[SERVER_STATUS.MAINTENANCE] },
]

// Pagination Constants
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_PER_PAGE: 20,
  PER_PAGE_OPTIONS: [20, 50, 100],
}

// API Response Status
export const API_STATUS = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
}

// Common Actions
export const ACTIONS = {
  CREATE: 'create',
  READ: 'read',
  UPDATE: 'update',
  DELETE: 'delete',
  VIEW: 'view',
  MANAGE: 'manage',
}

// Module Names
export const MODULES = {
  USERS: 'users',
  ROLES: 'roles',
  PERMISSIONS: 'permissions',
  CMS: 'cms',
  SETTINGS: 'settings',
}

// Permission types for UI display
export const PERMISSION_TYPES = {
  VIEW: 'view',
  CREATE: 'create',
  EDIT: 'edit',
  DELETE: 'delete',
}

// File Upload Constants
export const FILE_UPLOAD = {
  MAX_SIZE: 2 * 1024 * 1024, // 2MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif'],
}

// Date Format Constants
export const DATE_FORMAT = {
  DISPLAY: 'DD/MM/YYYY',
  API: 'YYYY-MM-DD',
  DATETIME: 'DD/MM/YYYY HH:mm:ss',
  API_DATETIME: 'YYYY-MM-DD HH:mm:ss',
}

// Validation Constants
export const VALIDATION = {
  PASSWORD_MIN_LENGTH: 6,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  EMAIL_MAX_LENGTH: 255,
}

// Sort Filter Constants
export const SORT_FILTER = {
  DESC: 'desc',
  ASC: 'asc',
}

// Time Period Constants for Overview Analytics
export const TIME_PERIOD = {
  TODAY: 1,        // Hôm nay
  YESTERDAY: 2,    // Hôm qua
  LAST_7_DAYS: 3,  // 7 Ngày trước
  LAST_30_DAYS: 4, // 30 Ngày trước
  THIS_MONTH: 5,   // Tháng này
}

export const TIME_PERIOD_LABELS = {
  [TIME_PERIOD.TODAY]: 'Hôm nay',
  [TIME_PERIOD.YESTERDAY]: 'Hôm qua',
  [TIME_PERIOD.LAST_7_DAYS]: '7 ngày qua',
  [TIME_PERIOD.LAST_30_DAYS]: '30 ngày qua',
  [TIME_PERIOD.THIS_MONTH]: 'Tháng này',
}

export const TIME_PERIOD_OPTIONS = [
  { value: TIME_PERIOD.TODAY, label: TIME_PERIOD_LABELS[TIME_PERIOD.TODAY] },
  { value: TIME_PERIOD.YESTERDAY, label: TIME_PERIOD_LABELS[TIME_PERIOD.YESTERDAY] },
  { value: TIME_PERIOD.LAST_7_DAYS, label: TIME_PERIOD_LABELS[TIME_PERIOD.LAST_7_DAYS] },
  { value: TIME_PERIOD.LAST_30_DAYS, label: TIME_PERIOD_LABELS[TIME_PERIOD.LAST_30_DAYS] },
  { value: TIME_PERIOD.THIS_MONTH, label: TIME_PERIOD_LABELS[TIME_PERIOD.THIS_MONTH] },
]

// Overview Type Constants
export const OVERVIEW_TYPE = {
  REGISTERED_ACCOUNTS: 1, // Số tài khoản đăng ký
  REGISTERED_IPS: 2,      // Số IP đăng ký
  TOTAL_REVENUE: 3,       // Tổng doanh thu
  TRANSACTION_COUNT: 4,   // Số lượng giao dịch
}

export const OVERVIEW_TYPE_LABELS = {
  [OVERVIEW_TYPE.REGISTERED_ACCOUNTS]: 'Số tài khoản đăng ký',
  [OVERVIEW_TYPE.REGISTERED_IPS]: 'Số IP đăng ký',
  [OVERVIEW_TYPE.TOTAL_REVENUE]: 'Doanh thu',
  [OVERVIEW_TYPE.TRANSACTION_COUNT]: 'Giao dịch thành công',
}

// Linkable Type Constants for Menu Items
export const LINKABLE_TYPE = {
  POST: 1,        // Bài viết
  CATEGORY: 2,    // Danh mục
  STATIC_PAGE: 3, // Trang tĩnh
}

export const LINKABLE_TYPE_LABELS = {
  [LINKABLE_TYPE.POST]: 'Bài viết',
  [LINKABLE_TYPE.CATEGORY]: 'Danh mục',
  [LINKABLE_TYPE.STATIC_PAGE]: 'Trang tĩnh',
}
