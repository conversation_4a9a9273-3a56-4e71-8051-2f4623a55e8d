import { useAuthStore } from '@/state/index.js'
import { storeToRefs } from 'pinia'

// Helper function để lọc menu theo quyền
export function filterMenuByPermissions(menuItems, userPermissions = []) {
  return menuItems
    .map((item) => {
      // Luôn lọc children nếu có
      let filteredChildren = []
      if (item.children) {
        filteredChildren = filterMenuByPermissions(item.children, userPermissions)
      }

      // Kiểm tra quyền của parent
      const hasParentPermission =
        item.permissions && item.permissions.length > 0
          ? item.permissions.some((permission) => userPermissions.includes(permission))
          : true // cho phép qua nếu không có permissions hoặc permissions rỗng

      // Quyết định hiển thị:
      // - Nếu parent có quyền, hoặc có ít nhất 1 child có quyền, thì hiển thị
      if (hasParentPermission || filteredChildren.length > 0) {
        return {
          ...item,
          // <PERSON><PERSON>n cập nhật lại children đã lọc
          children: filteredChildren.length > 0 ? filteredChildren : undefined,
        }
      }
      return null
    })
    .filter(Boolean)
}

// Helper function để kiểm tra quyền cụ thể
export function hasPermission(requiredPermissions, userPermissions = []) {
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return true
  }

  const hasAccess = requiredPermissions.some((permission) => userPermissions.includes(permission))

  return hasAccess
}

// Helper function để lấy quyền từ user object
export function getUserPermissions(user) {
  if (!user) {
    return []
  }

  // Nếu user có trường permissions trực tiếp
  if (user.permissions && Array.isArray(user.permissions)) {
    return user.permissions
  }

  // Nếu user có roles và roles có permissions
  if (user.roles && Array.isArray(user.roles)) {
    const permissions = []
    user.roles.forEach((role, index) => {
      if (role.permissions && Array.isArray(role.permissions)) {
        permissions.push(...role.permissions)
      } else {
        console.warn(`Role ${index} does not have permissions array`, role)
      }
    })
    const uniquePermissions = [...new Set(permissions)]
    return uniquePermissions // Remove duplicates
  }

  return []
}

// Helper function để tạo route guard kiểm tra quyền
export function permissionGuard(requiredPermissions) {
  return function(_to, _from, next) {
    const authStore = useAuthStore()
    const { authUser } = storeToRefs(authStore)
    const userPermissions = getUserPermissions(authUser?.value)

    if (!hasPermission(requiredPermissions, userPermissions)) {
      return next({ name: 'forbidden' })
    }
    next()
  }
}

// Helper function để tạo route guard với redirect có điều kiện
export function RedirectGuard(requiredPermissions, successRoute, fallbackRoute) {
  return function(_to, _from, next) {
    const authStore = useAuthStore()
    const { authUser } = storeToRefs(authStore)
    const userPermissions = getUserPermissions(authUser?.value)

    return next({ name: hasPermission(requiredPermissions, userPermissions) ? successRoute : fallbackRoute })
  }
}
