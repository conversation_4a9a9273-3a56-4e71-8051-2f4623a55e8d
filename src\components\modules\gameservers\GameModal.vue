<template>
  <Modal
    v-model="modalVisible"
    @close="handleClose"
    :title="editingGame ? 'Chỉnh sửa game' : 'Thêm game mới'"
    width="600px"
  >
    <div class="p-6">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        @submit.prevent="handleSubmit"
        class="space-y-6"
        label-position="top"
        size="large"
      >
        <!-- Basic Information -->
        <div class="space-y-6">
          <!-- Thumbnail Upload -->
          <FormField label="Thumbnail Game">
            <el-upload
              :before-upload="handleBeforeUpload"
              :http-request="handleThumbnailUpload"
              :show-file-list="false"
              accept="image/*"
              drag
              class="w-full"
            >
              <div v-if="!formData.thumbnail_url && !formData.thumbnail_preview" class="py-8 text-center">
                <Plus class="mx-auto mb-2 h-8 w-8 text-gray-400" />
                <div class="text-gray-600 dark:text-gray-300">Kéo thả ảnh thumbnail vào đây hoặc <em>click để chọn</em></div>
                <div class="mt-1 text-xs text-gray-400">Hỗ trợ: JPG, PNG, GIF (tối đa 5MB, khuyến nghị: 200x200px)</div>
                <div v-if="editingGame && editingGame?.thumb" class="mt-2 text-xs text-blue-500">
                  Để trống để giữ ảnh hiện tại
                </div>
              </div>
              <div v-else class="group relative">
                <img
                  :src="formData.thumbnail_preview || formData.thumbnail_url"
                  alt="Game Thumbnail"
                  class="mx-auto max-h-32 max-w-full rounded object-cover"
                />
                <div
                  class="bg-opacity-50 absolute inset-0 flex items-center justify-center rounded bg-black opacity-0 transition-opacity group-hover:opacity-100"
                >
                  <div class="flex gap-2">
                    <ButtonCommon type="primary" size="small">Thay đổi</ButtonCommon>
                    <ButtonCommon v-if="formData.thumbnail_url || formData.thumbnail_preview" type="danger" size="small" @click.stop="clearThumbnail">
                      Xóa
                    </ButtonCommon>
                  </div>
                </div>
              </div>
            </el-upload>
          </FormField>

          <!-- Name Field -->
          <FormField label="Tên game" required :error="errors.name">
            <el-form-item prop="name" class="!mb-0">
              <el-input
                v-model="formData.name"
                placeholder="Nhập tên game"
                :disabled="saving"
                clearable
                @input="handleNameInput"
              />
            </el-form-item>
          </FormField>

          <!-- Slug Field -->
          <FormField label="Slug" required :error="errors.slug">
            <el-form-item prop="slug" class="!mb-0">
              <el-input
                v-model="formData.slug"
                placeholder="Nhập slug (tự động tạo từ tên)"
                :disabled="saving"
                clearable
                @input="handleSlugInput"
              />
            </el-form-item>
          </FormField>

          <!-- Status Field -->
          <FormField label="Trạng thái" required>
            <el-form-item prop="status" class="!mb-0">
              <el-select
                v-model="formData.status"
                placeholder="Chọn trạng thái"
                :disabled="saving"
                class="w-full"
              >
                <el-option
                  v-for="option in statusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </FormField>

          <!-- Meta Data Field -->
          <FormField label="Meta Data" :error="metaDataError">
            <el-form-item prop="meta_data" class="!mb-0">
              <el-input
                v-model="formData.meta_data"
                type="textarea"
                :rows="6"
                placeholder="Nhập meta data dưới dạng JSON (tùy chọn)"
                :disabled="saving"
                clearable
              />
              <div v-if="metaDataError" class="mt-1 text-sm text-red-500">
                {{ metaDataError }}
              </div>
              <div class="mt-1 text-xs text-gray-500">
                Định dạng JSON hợp lệ. Ví dụ: {"url_center": "https://api.example.com", "username": "admin"}
              </div>
            </el-form-item>
          </FormField>
        </div>
      </el-form>

      <!-- Action Buttons -->
      <ButtonModalCommon
        :loading="saving"
        :can-submit="canSubmit"
        cancel-text="Hủy"
        :submit-text="editingGame ? 'Cập nhật' : 'Thêm mới'"
        loading-text="Đang lưu..."
        @cancel="handleClose"
        @submit="handleSubmit"
      />
    </div>
  </Modal>
</template>

<script setup>
import { ref, computed, watch, reactive } from 'vue'
import { ElMessage, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElUpload } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import Modal from '@/components/common/Modal.vue'
import FormField from '@/components/common/FormField.vue'
import ButtonModalCommon from '@/components/common/ButtonModalCommon.vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import { GAME_STATUS, GAME_STATUS_OPTIONS } from '@/utils/configs/constant.config.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  editingGame: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'close', 'save'])

// State
const formRef = ref(null)
const saving = ref(false)
const errors = ref({})
const metaDataError = ref('')

// Modal visibility computed property
const modalVisible = computed({
  get: () => {
    return props.modelValue
  },
  set: (value) => {
    emit('update:modelValue', value)
    if (!value) {
      emit('close')
    }
  }
})

// Form data
const formData = reactive({
  name: '',
  slug: '',
  status: GAME_STATUS.ACTIVE,
  meta_data: '',
  thumbnail_url: '',
  thumbnail_preview: '', // For preview
  thumbnail_file: null // Store file object
})

// Status options from constants
const statusOptions = GAME_STATUS_OPTIONS

// Validation rules
const rules = {
  name: [
    { required: true, message: 'Tên game là bắt buộc', trigger: 'blur' },
    { min: 2, message: 'Tên game phải có ít nhất 2 ký tự', trigger: 'change' }
  ],
  slug: [
    { required: true, message: 'Slug là bắt buộc', trigger: 'blur' },
    { 
      pattern: /^[a-z0-9-]+$/, 
      message: 'Slug chỉ được chứa chữ thường, số và dấu gạch ngang', 
      trigger: 'change' 
    }
  ],
  status: [
    { required: true, message: 'Trạng thái là bắt buộc', trigger: 'change' }
  ]
}

// Computed
const isValidMetaData = computed(() => {
  if (!formData.meta_data.trim()) return true // Empty is valid
  
  try {
    JSON.parse(formData.meta_data)
    metaDataError.value = ''
    return true
  } catch (error) {
    metaDataError.value = 'Meta data không đúng định dạng JSON'
    return false
  }
})

const canSubmit = computed(() => {
  const nameValid = !!formData.name?.trim()
  const slugValid = !!formData.slug?.trim()
  const statusValid = formData.status !== null && formData.status !== undefined
  const metaDataValid = isValidMetaData.value
  
  return nameValid && slugValid && statusValid && metaDataValid
})

// Auto-generate slug from name
const generateSlug = (name) => {
  return name
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove accents
    .replace(/[^a-z0-9\s-]/g, '') // Remove special chars
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

// Input handlers for real-time validation
const handleNameInput = (value) => {
  formData.name = value
  
  // Auto-generate slug if not editing and name has content
  if (!props.editingGame && value.trim()) {
    formData.slug = generateSlug(value)
  }
  
  // Clear validation if name is valid
  if (value?.trim()?.length >= 2) {
    delete errors.value.name
  }
}

const handleSlugInput = (value) => {
  formData.slug = value
  
  // Clear validation if slug is valid
  if (value?.trim() && /^[a-z0-9-]+$/.test(value)) {
    delete errors.value.slug
  }
}

// Watch for modal opening/closing
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    resetForm()
    if (props.editingGame) {
      loadGameData()
    }
  }
})

// Methods
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    slug: '',
    status: GAME_STATUS.ACTIVE,
    meta_data: '',
    thumbnail_url: '',
    thumbnail_preview: '',
    thumbnail_file: null
  })
  
  errors.value = {}
  metaDataError.value = ''
  
  // Clear form validation
  setTimeout(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  }, 100)
}

const loadGameData = () => {
  if (props.editingGame) {
    Object.assign(formData, {
      name: props.editingGame?.name || '',
      slug: props.editingGame?.slug || '',
      status: props.editingGame?.status === 'active' ? GAME_STATUS.ACTIVE : GAME_STATUS.INACTIVE,
      meta_data: props.editingGame?.meta_data ? JSON.stringify(props.editingGame.meta_data, null, 2) : '',
      thumbnail_url: props.editingGame?.thumb || '', // Use 'thumb' from API response
      thumbnail_preview: '',
      thumbnail_file: null
    })
  }
}

const handleSubmit = async () => {
  if (!canSubmit.value) {
    ElMessage.error('Vui lòng kiểm tra lại thông tin')
    return
  }

  // Validate form
  try {
    await formRef.value.validate()
  } catch (error) {
    ElMessage.error('Vui lòng kiểm tra lại thông tin')
    return
  }

  // Check meta data validity
  if (formData.meta_data.trim() && !isValidMetaData.value) {
    ElMessage.error('Meta Data JSON không hợp lệ')
    return
  }

  saving.value = true
  
  try {
    // Create FormData for file upload support
    const formDataToSend = new FormData()
    
    // Add basic game data
    formDataToSend.append('name', formData.name || '')
    formDataToSend.append('slug', formData.slug || '')
    formDataToSend.append('status', formData.status.toString()) // Convert to string for API
    
    // Add thumbnail file if selected
    if (formData.thumbnail_file) {
      formDataToSend.append('thumb', formData.thumbnail_file)
    }
    
    // Add meta_data if provided
    if (formData.meta_data.trim()) {
      formDataToSend.append('meta_data', formData.meta_data)
    }

    emit('save', formDataToSend)
    
  } catch (error) {
    console.error('Form submit error:', error)
    ElMessage.error('Có lỗi xảy ra khi lưu dữ liệu')
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  emit('close')
}

// File upload handlers
const handleBeforeUpload = (file) => {
  const isValidSize = file.size / 1024 / 1024 < 5 // 5MB
  if (!isValidSize) {
    ElMessage.error('Kích thước file không được vượt quá 5MB!')
    return false
  }
  
  const isValidType = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)
  if (!isValidType) {
    ElMessage.error('Chỉ hỗ trợ file ảnh JPG, PNG, GIF, WEBP!')
    return false
  }
  
  return true
}

const handleThumbnailUpload = async (options) => {
  try {
    // Store the file object for form submission
    formData.thumbnail_file = options.file
    
    // Create preview URL
    const reader = new FileReader()
    reader.onload = (e) => {
      formData.thumbnail_preview = e.target.result
    }
    reader.readAsDataURL(options.file)
    
  } catch (error) {
    console.error('Upload error:', error)
    ElMessage.error('Có lỗi xảy ra khi tải ảnh')
  }
}

const clearThumbnail = () => {
  formData.thumbnail_url = ''
  formData.thumbnail_preview = ''
  formData.thumbnail_file = null
}
</script>

<style lang="scss" scoped>
/* Upload area styling */
:deep(.el-upload-dragger) {
  border: 2px dashed var(--el-border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-upload-dragger:hover) {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

/* Form validation styling */
:deep(.el-form-item.is-error .el-input__wrapper) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

:deep(.el-form-item.is-error .el-select .el-input__wrapper) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}
</style>
