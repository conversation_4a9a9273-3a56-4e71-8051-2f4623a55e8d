<template>
  <div class="date-range-wrapper flex items-center gap-2 w-full md:w-auto" ref="datePickerContainer">
    <el-date-picker
      ref="datePickerRef"
      v-model="internalDateRange"
      type="daterange"
      range-separator="đến"
      start-placeholder="Từ ngày"
      end-placeholder="Đến ngày"
      format="DD/MM/YYYY"
      value-format="YYYY-MM-DD"
      @change="handleDateRangeChange"
      @clear="handleDateRangeClear"
      @focus="handleDatePickerFocus"
      size="small"
      class="date-picker-mobile"
      :shortcuts="shortcuts"
      :editable="false"
      :unlink-panels="true"
      :teleported="false"
      :popper-class="'analytics-date-popper'"
      :popper-options="{
        placement: 'bottom-start',
        strategy: 'absolute',
        modifiers: [
          {
            name: 'preventOverflow',
            options: {
              boundary: 'viewport',
              padding: 8
            }
          },
          {
            name: 'flip',
            options: {
              fallbackPlacements: ['bottom-start', 'bottom-end', 'top-start', 'top-end']
            }
          }
        ]
      }"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  shortcuts: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'clear', 'focus'])

// Refs
const datePickerRef = ref(null)
const datePickerContainer = ref(null)

// Internal date range (v-model proxy)
const internalDateRange = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

// Handle date range change
const handleDateRangeChange = (value) => {
  emit('change', value)
}

// Handle date range clear
const handleDateRangeClear = () => {
  emit('clear')
}

// Handle date picker focus
const handleDatePickerFocus = () => {
  emit('focus')
}

// Expose refs for parent components
defineExpose({
  datePickerRef,
  datePickerContainer
})
</script>

<style lang="scss" scoped>
// Date range wrapper for full width on mobile
.date-range-wrapper {
  width: 100%;
}

// El-date-picker styling with mobile responsive
.date-picker-mobile {
  width: 100% !important;
  min-width: 180px;
  max-width: 100%;
  
  @media (min-width: 480px) {
    min-width: 200px;
    max-width: 280px;
  }
  
  @media (min-width: 640px) {
    max-width: 280px;
  }
  
  @media (min-width: 768px) {
    width: 240px !important;
    max-width: 240px;
  }
}

:global(.analytics-date-popper) {
  z-index: 100001 !important;
}

// Ghi đè mạnh mẽ cho mobile date picker
:deep(.el-date-editor) {
  width: 100% !important;
  max-width: 100% !important;
  
  .el-input__wrapper {
    width: 100% !important;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    
    &:hover {
      border-color: #9ca3af;
    }
    
    &.is-focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
  
  // Mobile responsive text size
  .el-input__inner {
    font-size: 13px;
    width: 100% !important;
    
    @media (max-width: 479px) {
      font-size: 11px;
    }
    
    @media (max-width: 767px) {
      font-size: 12px;
    }
  }
  
  // Prevent overflow on mobile
  .el-range-separator {
    @media (max-width: 479px) {
      font-size: 10px;
      padding: 0 1px;
    }
    
    @media (max-width: 640px) {
      font-size: 11px;
      padding: 0 2px;
    }
  }
  
  .el-range-input {
    @media (max-width: 479px) {
      font-size: 11px;
    }
    
    @media (max-width: 640px) {
      font-size: 12px;
    }
  }
}

// Ghi đè mạnh mẽ cho picker panel trên mobile
:global(.el-picker-panel) {
  max-width: 100% !important;
  
  @media (max-width: 768px) {
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    border-radius: 0 !important;
    position: fixed !important;
    top: auto !important;
    bottom: 0 !important;
    max-height: 70vh !important;
    overflow-y: auto !important;
    z-index: 100002 !important;
  }
}

// Ghi đè cho date editor mobile
@media (max-width: 768px) {
  :deep(.el-date-editor) {
    width: 100% !important;
  }
}

// Mobile responsive shortcuts popover
:deep(.el-picker-panel__shortcut) {
  @media (max-width: 479px) {
    font-size: 11px;
    padding: 6px 8px;
  }
  
  @media (max-width: 767px) {
    font-size: 12px;
    padding: 8px 12px;
  }
}

// Additional mobile responsiveness
:deep(.el-popper) {
  @media (max-width: 479px) {
    max-width: 90vw !important;
  }
  
  @media (max-width: 768px) {
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    border-radius: 0 !important;
    position: fixed !important;
    bottom: 0 !important;
    top: auto !important;
    transform: none !important;
    max-height: 70vh !important;
    overflow-y: auto !important;
  }
}

// Ghi đè mạnh cho date picker panel
:global(.el-date-range-picker) {
  @media (max-width: 768px) {
    width: 100% !important;
    margin: 0 !important;
    border-radius: 0 !important;
  }
}

// Đảm bảo overlay trên mobile
:global(.el-overlay) {
  @media (max-width: 768px) {
    z-index: 100001 !important;
  }
}
</style>
