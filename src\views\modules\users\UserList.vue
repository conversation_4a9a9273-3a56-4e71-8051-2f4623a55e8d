<template>
  <div class="user-list-wrapper">
    <PageBreadcrumb :page-title="currentPageTitle" :breadcrumbs="[{ label: 'Trang chủ', to: '/' }]" />

    <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-700 dark:bg-[#1e2636]">
      <!-- Page Header -->

      <!-- Filters -->
      <div class="mb-6 rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-600 dark:bg-gray-700/50">
        <div class="flex flex-wrap items-center gap-4">
          <div class="max-w-[300px] min-w-[200px] flex-1">
            <el-input
              v-model="filters.search"
              placeholder="Tìm kiếm theo tên hoặc email..."
              clearable
              @input="handleSearch"
              class=""
            >
              <template #prefix>
                <i class="el-icon-search text-gray-400 dark:text-gray-300"></i>
              </template>
            </el-input>
          </div>

          <div class="max-w-[200px] min-w-[150px]">
            <el-select
              v-model="filters.status"
              placeholder="Trạng thái"
              clearable
              @change="handleFilterChange"
              class="w-full"
            >
              <el-option
                v-for="status in statusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </div>

          <div class="max-w-[200px] min-w-[150px]">
            <el-select
              v-model="filters.role_id"
              placeholder="Vai trò"
              clearable
              @change="handleFilterChange"
              class="w-full"
            >
              <el-option v-for="role in allRoles" :key="role.id" :label="role.name" :value="role.id" />
            </el-select>
          </div>

          <div class="ml-auto flex items-center gap-2">
            <ButtonCommon
              text="Tạo người dùng"
              type="primary"
              size="medium"
              :icon="PlusIcon"
              @click="handleCreateUser"
            />
          </div>
        </div>
      </div>

      <!-- Users Table -->
      <div
        class="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-[#1e2636]"
      >
        <el-table
          v-loading="loading"
          :data="users"
          style="width: 100%"
          :header-cell-style="getTableHeaderStyle"
          :cell-style="getTableCellStyle"
          table-layout="auto"
          class="user-table"
          :row-class-name="getRowClassName"
        >
          <el-table-column label="Người dùng" min-width="250">
            <template #default="{ row }">
              <div class="flex items-center">
                <el-avatar :src="row.avatar" :alt="row.name" class="mr-3" :size="40">
                  {{ row.name.charAt(0).toUpperCase() }}
                </el-avatar>
                <div>
                  <div class="font-medium text-gray-900 dark:text-white">{{ row.name }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">{{ row.email }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="Trạng thái" width="200">
            <template #default="{ row }">
              <div class="flex flex-wrap justify-center gap-1">
                <el-tag :type="getStatusType(row.status)" size="small" class="status-tag">
                  {{ getStatusLabel(row.status) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="Vai trò" min-width="200">
            <template #default="{ row }">
              <div class="flex flex-wrap justify-center gap-1">
                <el-tag v-for="role in row.roles" :key="role.id" size="small" type="info" class="role-tag">
                  {{ role.name }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="Ngày tạo" width="150">
            <template #default="{ row }">
              <div class="text-center text-sm text-gray-600 dark:text-gray-300">
                {{ formatDate(row.created_at) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="Thao tác" width="180" fixed="right">
            <template #default="{ row }">
              <div class="flex items-center whitespace-nowrap">
                <el-tooltip content="Sửa người dùng" placement="top">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleEditUser(row)"
                    class="action-btn edit-btn"
                    v-if="canEditUser"
                  >
                    <EditIcon class="mr-1 h-4 w-4" />
                    Sửa
                  </el-button>
                </el-tooltip>

                <el-tooltip content="Đổi mật khẩu" placement="top">
                  <el-button
                    type="warning"
                    size="small"
                    @click="handleChangePassword(row)"
                    class="action-btn password-btn"
                    v-if="canChangePassword"
                  >
                    <KeyIcon class="mr-1 h-4 w-4" />
                    Mật khẩu
                  </el-button>
                </el-tooltip>

                <el-tooltip content="Xóa người dùng" placement="top">
                  <el-button
                    type="danger"
                    size="small"
                    @click="handleDeleteUser(row)"
                    class="action-btn delete-btn"
                    v-if="canDeleteUser"
                  >
                    <TrashIcon class="mr-1 h-4 w-4" />
                    Xóa
                  </el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="border-t border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-700/50">
          <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <!-- Left side: Info and page size selector -->
            <div class="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4">
              <span class="text-sm text-gray-700 dark:text-gray-300">
                Hiển thị {{ pagination.from }} - {{ pagination.to }} của {{ pagination.total }} kết quả
              </span>
              <div class="flex items-center gap-2">
                <el-select
                  v-model="filters.per_page"
                  @change="handleSizeChange"
                  style="width: 80px"
                  size="small"
                  class=""
                >
                  <el-option v-for="option in perPageOptions" :key="option" :label="option" :value="option" />
                </el-select>
                <span class="text-sm text-gray-700 dark:text-gray-300">/ trang</span>
              </div>
            </div>

            <!-- Right side: Pagination controls -->
            <div class="flex justify-center sm:justify-end">
              <el-pagination
                v-model:current-page="filters.page"
                :total="pagination.total"
                :page-size="filters.per_page"
                layout="prev, pager, next"
                @current-change="handleCurrentChange"
                small
                class="custom-pagination"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- User Form Modal -->
    <UserModal
      v-model:visible="showUserForm"
      :user="selectedUser"
      :roles="allRoles"
      :loading-roles="loadingAllRoles"
      @success="handleUserFormSuccess"
    />

    <!-- Change Password Modal -->
    <ChangePasswordModal
      v-model:visible="showChangePassword"
      :user="selectedUser"
      @success="handleChangePasswordSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useUsers } from '@/composables/modules/users/useUsers.js'
import { UserModal, ChangePasswordModal } from '@/components/pages/users'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import { PlusIcon, EditIcon, KeyIcon, TrashIcon, RefreshIcon } from '@/components/icons/index.js'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import { PAGINATION } from '@/utils/configs/constant.config.js'
import { hasPermission, getUserPermissions } from '@/utils/helpers/permission.helper.js'
import { useAuthStore } from '@/state/index.js'
import { storeToRefs } from 'pinia'
import { formatDate } from '@/utils/helpers/datetime.helper.js'

// Page title
const currentPageTitle = ref('Quản lý người dùng')

// Composables
const {
  loading,
  users,
  pagination,
  filters,
  fetchUsers,
  deleteUser,
  resetFilters,
  USER_STATUS_OPTIONS,
  fetchAllRoles,
  allRoles,
  loadingAllRoles,
} = useUsers()

// Local state
const showUserForm = ref(false)
const showChangePassword = ref(false)
const selectedUser = ref(null)

// Computed
const statusOptions = computed(() => USER_STATUS_OPTIONS)
const perPageOptions = computed(() => PAGINATION.PER_PAGE_OPTIONS)

// Enhanced table styles based on TailAdmin demo
const getTableHeaderStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color-page)',
    color: 'var(--el-text-color-primary)',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: '14px',
    padding: '16px 12px',
    borderBottom: '1px solid var(--el-border-color-light)',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
  }
}

const getTableCellStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color)',
    color: 'var(--el-text-color-primary)',
    borderBottom: '1px solid var(--el-border-color-lighter)',
    padding: '16px 12px',
    fontSize: '14px',
  }
}

const getRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// Methods
const handleSearch = () => {
  filters.value.page = 1
  fetchUsers()
}

const handleFilterChange = () => {
  filters.value.page = 1
  fetchUsers()
}

const handleCreateUser = async () => {
  selectedUser.value = null
  await fetchAllRoles()
  showUserForm.value = true
}

const handleEditUser = async (user) => {
  selectedUser.value = user
  await fetchAllRoles()
  showUserForm.value = true
}

const handleChangePassword = (user) => {
  selectedUser.value = user
  showChangePassword.value = true
}

const handleDeleteUser = async (user) => {
  await deleteUser(user.id)
  fetchUsers()
}

const handleUserFormSuccess = () => {
  showUserForm.value = false
  selectedUser.value = null
  fetchUsers()
}

const handleChangePasswordSuccess = () => {
  showChangePassword.value = false
  selectedUser.value = null
}

const handleSizeChange = (size) => {
  filters.value.per_page = size
  filters.value.page = 1
  fetchUsers()
}

const handleCurrentChange = (page) => {
  filters.value.page = page
  fetchUsers()
}

const handleResetFilters = () => {
  resetFilters()
  fetchUsers()
}

const getStatusType = (status) => {
  const statusMap = {
    1: 'success', // ACTIVE
    0: 'warning', // INACTIVE
    2: 'danger', // SUSPENDED
  }
  return statusMap[status] || 'info'
}

const getStatusLabel = (status) => {
  const option = statusOptions.value.find((opt) => opt.value === status)
  return option ? option.label : `Trạng thái ${status}`
}

// Lifecycle
onMounted(async () => {
  await fetchUsers()
  await fetchAllRoles()
})

const authStore = useAuthStore()
const { authUser } = storeToRefs(authStore)
const userPermissions = getUserPermissions(authUser.value)

const canCreateUser = hasPermission(['user_management.create'], userPermissions)
const canEditUser = hasPermission(['user_management.edit'], userPermissions)
const canDeleteUser = hasPermission(['user_management.delete'], userPermissions)
const canChangePassword = hasPermission(['user_management.edit'], userPermissions)
</script>

<style lang="scss">
@use '@/assets/styles/modules/users/user-list' as *;
</style>
