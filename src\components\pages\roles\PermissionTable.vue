<template>
  <div class="permission-table flex h-full flex-col">
    <!-- Scrollable Content Area -->
    <!-- Compact Header Section -->
    <div
      class="sticky top-0 z-10 rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 px-3 py-2 sm:px-4 sm:py-3 dark:from-gray-800 dark:to-gray-900"
    >
      <!-- Role Info Slot (if provided) -->
      <div v-if="$slots['role-info']" class="pt-1">
        <slot name="role-info" />
      </div>
    </div>

    <div class="mt-2 min-h-0 min-w-0 flex-1">
      <div class="roles-container role-list-scroll h-full w-full">
        <!--  -->

        <div element-loading-text="Đang tải..." class="relative h-full w-full" v-loading="permissionsLoading">
          <div v-if="!localPermissions || localPermissions.length === 0" class="py-4 text-center sm:py-6 md:py-8">
            <div class="flex flex-col items-center gap-2 sm:gap-3">
              <div
                class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100 sm:h-12 sm:w-12 dark:bg-gray-800"
              >
                <el-icon class="text-lg text-gray-400 sm:text-xl">
                  <Document />
                </el-icon>
              </div>
              <div class="text-sm font-medium text-gray-700 sm:text-base dark:text-gray-300">
                Không có dữ liệu nhóm quyền hạn
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Chưa có nhóm quyền hạn nào được định nghĩa cho vai trò này
              </div>
            </div>
          </div>

          <!-- Check đã chọn chưa -->
          <div v-else-if="!selectedRole" class="py-4 text-center sm:py-6 md:py-8">
            <div class="flex flex-col items-center gap-2 sm:gap-3">
              <div
                class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 sm:h-12 sm:w-12 dark:bg-blue-900"
              >
                <el-icon class="text-lg text-blue-500 sm:text-xl">
                  <User />
                </el-icon>
              </div>
              <div class="text-sm font-medium text-gray-700 sm:text-base dark:text-gray-300">
                Vui lòng chọn một vai trò
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Chọn vai trò từ danh sách bên trái để xem phân quyền
              </div>
            </div>
          </div>

          <!-- Check -->
          <div class="" v-if="localPermissions && localPermissions.length > 0 && selectedRole">
            <el-card v-for="group in tableData" :key="group.id" class="permission-card my-4">
              <!-- header -->
              <template #header>
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-2">
                    <!-- Expand/Collapse Icon -->
                    <div
                      v-if="group.hasChildren || (group.children && group.children.length > 0)"
                      class="flex-shrink-0"
                    >
                      <el-tooltip
                        :content="isRowExpanded(group) ? 'Thu gọn nhóm' : 'Mở rộng nhóm'"
                        placement="top"
                        effect="dark"
                        :show-after="500"
                      >
                        <div
                          class="expand-button-wrapper"
                          @click="toggleRowExpansion(group)"
                          @keydown.enter="toggleRowExpansion(group)"
                          @keydown.space="toggleRowExpansion(group)"
                          tabindex="0"
                          role="button"
                          :aria-label="isRowExpanded(group) ? 'Thu gọn nhóm' : 'Mở rộng nhóm'"
                          :aria-expanded="isRowExpanded(group)"
                        >
                          <el-icon class="expand-icon" :class="{ 'rotate-90': isRowExpanded(group) }">
                            <ArrowRightIcon />
                          </el-icon>
                        </div>
                      </el-tooltip>
                    </div>
                    <div v-else class="w-3 flex-shrink-0 sm:w-4"></div>

                    <!-- Group Icon -->
                    <div
                      class="flex h-6 w-6 items-center justify-center rounded-lg bg-gradient-to-br from-blue-100 to-indigo-100 sm:h-7 sm:w-7 dark:from-blue-900 dark:to-indigo-900"
                    >
                      <el-icon class="text-xs text-blue-600 sm:text-sm dark:text-blue-400">
                        <Folder />
                      </el-icon>
                    </div>

                    <span class="text-lg font-semibold text-gray-800 dark:text-gray-200">{{ group.name }}</span>
                  </div>

                  <div class="flex items-center justify-center space-x-3">
                    <span class="text-sm text-gray-600 dark:text-gray-400">
                      Đã cấp:
                      <span class="font-medium text-green-600 dark:text-green-400">{{
                        getTotalSelectedByCode(group.code)
                      }}</span>
                      / <span class="font-medium">{{ getTotalPermissionsByCode(group.code) }}</span> quyền
                    </span>

                    <el-checkbox
                      v-if="getTotalPermissionsByCode(group.code) > 0"
                      label="Chọn tất cả"
                      :disabled="!props.canAssign || isRoleProtected"
                      :model-value="getGroupSelectionState(group.code).isSelected"
                      :indeterminate="getGroupSelectionState(group.code).isIndeterminate"
                      @change="() => handleGroupSelectAllChange(group.code)"
                    />
                  </div>
                </div>
              </template>

              <!-- permission -->
              <div class="custom-permission-table overflow-x-auto">
                <table class="permission-table min-w-full border-collapse whitespace-nowrap">
                  <tbody>
                    <!-- Hiển thị permissions của group chính -->
                    <PermissionRow
                      v-if="group.permissions && group.permissions.length > 0"
                      :group="group"
                      :is-role-protected="isRoleProtected"
                      :can-assign="canAssign"
                      :get-permission-by-type="getPermissionByType"
                      :get-permission-active-state="getPermissionActiveState"
                      @permission-change="handlePermissionChange"
                      :is-checkbox-disabled="isCheckboxDisabled"
                    />

                    <!-- Hiển thị thông báo cho parent groups không có permissions trực tiếp -->
                    <tr v-else-if="group.hasChildren && (!group.permissions || group.permissions.length === 0)">
                      <td colspan="2" class="px-4 py-3 text-center text-sm text-blue-600 dark:text-blue-400">
                        <div class="flex items-center justify-center gap-2">
                          <el-icon class="text-base">
                            <Folder />
                          </el-icon>
                          <span>Nhóm cha - Chứa {{ getDirectChildrenCount(group) }} nhóm con</span>
                        </div>
                      </td>
                    </tr>

                    <!-- Hiển thị children -->
                    <template
                      v-if="group.hasChildren && group.children && group.children.length > 0 && isRowExpanded(group)"
                    >
                      <PermissionRow
                        v-for="childGroup in group.children"
                        :key="childGroup.id"
                        :group="childGroup"
                        :is-role-protected="isRoleProtected"
                        :can-assign="canAssign"
                        :get-permission-by-type="getPermissionByType"
                        :get-permission-active-state="getPermissionActiveState"
                        :is-checkbox-disabled="isCheckboxDisabled"
                        @permission-change="handlePermissionChange"
                      />
                    </template>

                    <!-- Hiển thị thông báo chỉ cho group có children khi bị collapse -->
                    <tr v-if="group.children && group.children.length > 0 && !isRowExpanded(group)">
                      <td colspan="2" class="py-2 text-center text-sm text-gray-500 dark:text-gray-400">
                        Nhóm bị thu gọn - Click vào icon để mở rộng
                      </td>
                    </tr>

                    <!-- Hiển thị thông báo khi không có permissions và children (cho group không có children) -->
                    <tr
                      v-if="
                        (!group.permissions || group.permissions.length === 0) &&
                        (!group.children || group.children.length === 0)
                      "
                    >
                      <td colspan="2" class="py-3 text-center text-sm text-gray-500 dark:text-gray-400">
                        <div class="flex items-center justify-center gap-2">
                          <el-icon class="text-base">
                            <Document />
                          </el-icon>
                          <span>Nhóm trống - Chưa có quyền nào được định nghĩa</span>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

import { ArrowRight as ArrowRightIcon } from '@element-plus/icons-vue'
import { Folder, Document, User } from '@element-plus/icons-vue'
import { useRoles } from '@/composables/modules/users/useRoles.js'
import PermissionRow from './PermissionRow.vue'

// Props
const props = defineProps({
  selectedRole: {
    type: Object,
    default: null,
  },
  // Dữ liệu từ API sẽ có cấu trúc: { role: {...}, permission_groups: [...] }
  permissions: {
    type: [Array, Object],
    default: () => [],
  },
  permissionsLoading: {
    type: Boolean,
    default: false,
  },
  canAssign: {
    type: Boolean,
    default: true,
  },
})

// Emits
const emit = defineEmits(['permission-changed'])

// Composables
const { assignPermissions, revokePermissions, saving: savingPermissions } = useRoles()

// Local reactive state for permissions
const localPermissions = ref([])

// Tree table state
const tableData = ref([])
const expandedKeys = ref([])
const tableRef = ref(null)

// Loading state for individual permission changes
const changingPermissions = ref(new Set())

// Reactive dark mode state
const isDarkMode = ref(false)

// Function to check dark mode
const checkDarkMode = () => {
  isDarkMode.value = document.documentElement.classList.contains('dark')
}

// Check if selected role is protected
const isRoleProtected = computed(() => {
  if (!props.selectedRole) return false

  const isProtectedValue = props.selectedRole.is_protected
  return isProtectedValue === 1 || isProtectedValue === '1' || isProtectedValue === true || isProtectedValue === 'true'
})

// Count active permissions from local state
const activePermissionCount = computed(() => {
  if (!localPermissions.value || localPermissions.value.length === 0) return 0

  const countActivePermissions = (groups) => {
    let count = 0
    for (const group of groups) {
      // Count direct active permissions
      if (group.permissions && Array.isArray(group.permissions)) {
        count += group.permissions.filter((p) => p.active === true).length
      }
      // Count children active permissions recursively
      if (group.children && Array.isArray(group.children)) {
        count += countActivePermissions(group.children)
      }
    }
    return count
  }

  return countActivePermissions(localPermissions.value)
})

// Check if any checkbox should be disabled
const hasDisabledCheckboxes = computed(() => {
  return isRoleProtected.value || savingPermissions.value || changingPermissions.value.size > 0
})

// Method to check if a specific checkbox should be disabled
const isCheckboxDisabled = (permissionId) => {
  return savingPermissions.value || isRoleProtected.value || changingPermissions.value.has(permissionId)
}

// Add resize listener
onMounted(() => {
  // Check initial dark mode state
  checkDarkMode()

  // Create MutationObserver to watch for theme changes
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        checkDarkMode()
        // Force table to re-render with new styles
        nextTick(() => {
          tableRef.value?.doLayout()
        })
      }
    })
  })

  // Start observing document element for class changes
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class'],
  })

  // Store observer for cleanup
  window.themeObserver = observer

  // Watch for dark mode changes and force table update
  watch(isDarkMode, () => {
    nextTick(() => tableRef.value?.doLayout())
  })

  // Prevent page scroll on table interactions
  const tableBody = tableRef.value?.$el?.querySelector('.el-table__body-wrapper')
  if (tableBody) {
    const stopPropagation = (e) => e.stopPropagation()
    tableBody.addEventListener('scroll', stopPropagation)
    tableBody.addEventListener('wheel', stopPropagation)
  }
})

onUnmounted(() => {
  // Cleanup theme observer
  if (window.themeObserver) {
    window.themeObserver.disconnect()
    delete window.themeObserver
  }
})

// Methods
const hasGroupPermissions = (group) => {
  if (!group || !group.code) {
    return false
  }

  // Check if group has valid permissions
  const hasDirectPermissions =
    group.permissions &&
    Array.isArray(group.permissions) &&
    group.permissions.length > 0 &&
    group.permissions.some((p) => p && p.id && p.name)

  // Check if children have permissions recursively
  const hasChildrenWithPermissions =
    group.children &&
    Array.isArray(group.children) &&
    group.children.some((child) => {
      return child && hasGroupPermissions(child)
    })

  return hasDirectPermissions || hasChildrenWithPermissions
}

const transformDataForTreeTable = () => {
  if (!localPermissions.value || !Array.isArray(localPermissions.value)) {
    tableData.value = []
    return
  }

  const transformGroup = (group, level = 0) => {
    if (!group || !group.code) {
      return null
    }

    const hasChildren = group.children && Array.isArray(group.children) && group.children.length > 0
    // Kiểm tra nếu group có permissions hoặc children có permissions
    const hasDirectPermissions = group.permissions && Array.isArray(group.permissions) && group.permissions.length > 0

    return {
      id: group.code,
      name: group.name || '',
      code: group.code,
      permissions: group.permissions || [],
      hasChildren: hasChildren,
      children: hasChildren
        ? group.children.map((child) => transformGroup(child, level + 1)).filter(Boolean)
        : undefined,
      level: level,
      // Thêm flag để biết group này có permissions trực tiếp hay không
      hasDirectPermissions: hasDirectPermissions,
    }
  }

  // Transform permission_groups to tree table data với tất cả groups, kể cả không có permissions
  tableData.value = localPermissions.value
    .filter((group) => group && group.code) // Chỉ lọc group có code
    .map((group) => transformGroup(group, 0))
    .filter(Boolean)

}

// Hàm đếm tổng số permissions trong group (kể cả children)
const getTotalPermissionsByCode = (groupCode) => {
  const targetNode = findGroupByCode(tableData.value, groupCode)
  if (!targetNode) return 0

  return countTotalPermissions(targetNode)
}

// Utility functions for counting permissions (gộp logic đếm permissions)
const countPermissions = (group, countType = 'total') => {
  if (!group) return 0

  let count = 0

  // Đếm permissions trong node hiện tại
  if (group.permissions && Array.isArray(group.permissions)) {
    if (countType === 'total') {
      count += group.permissions.length
    } else if (countType === 'selected') {
      count += group.permissions.filter((p) => p.active === true).length
    }
  }

  // Đệ quy vào children nếu có
  if (group.children && Array.isArray(group.children)) {
    group.children.forEach((child) => {
      count += countPermissions(child, countType)
    })
  }

  return count
}

// Wrapper functions sử dụng countPermissions chung
const countTotalPermissions = (group) => countPermissions(group, 'total')
const countSelectedPermissions = (group) => countPermissions(group, 'selected')

// Các helper functions được tối ưu
const getDirectChildrenCount = (group) => {
  return group?.children?.length || 0
}

const getTotalSelectedByCode = (groupCode) => {
  if (!tableData.value || !Array.isArray(tableData.value)) return 0
  const targetGroup = findGroupByCode(tableData.value, groupCode)
  return targetGroup ? countSelectedPermissions(targetGroup) : 0
}

// Tìm Group theo groupCode
const findGroupByCode = (groups, groupCode) => {
  if (!groups || !Array.isArray(groups)) return null

  for (const node of groups) {
    if (node.code === groupCode) {
      return node
    }
    if (node.children && Array.isArray(node.children)) {
      const found = findGroupByCode(node.children, groupCode)
      if (found) return found
    }
  }

  return null
}

// Kiểm tra trạng thái selectAll và indeterminate
const getGroupSelectionState = (groupCode) => {
  if (!groupCode || !tableData.value) {
    return {
      isSelected: false,
      isIndeterminate: false,
    }
  }

  const targetGroup = findGroupByCode(tableData.value, groupCode)
  if (!targetGroup) {
    console.warn(`⚠️ Group with code '${groupCode}' not found`)
    return {
      isSelected: false,
      isIndeterminate: false,
    }
  }

  const totalPermissions = getTotalPermissionsByCode(groupCode)
  const selectedPermissions = getTotalSelectedByCode(groupCode)

  // Nếu không có permissions thì không hiển thị indeterminate
  if (totalPermissions === 0) {
    return {
      isSelected: false,
      isIndeterminate: false,
    }
  }

  return {
    isSelected: selectedPermissions === totalPermissions && totalPermissions > 0,
    isIndeterminate: selectedPermissions > 0 && selectedPermissions < totalPermissions,
  }
}

// Hàm chọn tất cả các permission
const handleGroupSelectAllChange = async (groupCode) => {
  const state = getGroupSelectionState(groupCode)
  const shouldSelectAll = state.isIndeterminate || !state.isSelected

  // lấy tẩt cả permission IDs trong group
  const targetGroup = findGroupByCode(tableData.value, groupCode)
  if (!targetGroup) return

  const permissionIds = getAllPermissionIdsInGroup(targetGroup)
  if (permissionIds.length === 0) return

  // check
  if (isRoleProtected.value) {
    ElMessage.warning('Không thể chỉnh sửa quyền hạn của vai trò được bảo vệ')
    return
  }

  try {
    // Thêm tất cả IDs vào changing set
    permissionIds.forEach((id) => changingPermissions.value.add(id))

    // Cập nhật local state ngay lập tức cho tất cả
    permissionIds.forEach((id) => {
      updateLocalPermissionState(id, shouldSelectAll)
    })

    // Emit event để UI cập nhật
    emit('permission-changed')

    // Gọi API batch - chỉ 1 lần gọi cho tất cả permissions
    if (shouldSelectAll) {
      await assignPermissions(props.selectedRole.id, permissionIds)
    } else {
      await revokePermissions(props.selectedRole.id, permissionIds)
    }
  } catch (error) {
    console.error('❌ Group select all operation failed:', error)
    ElMessage.error('Không thể cập nhật quyền hạn nhóm')

    // Revert tất cả nếu có lỗi
    permissionIds.forEach((id) => {
      updateLocalPermissionState(id, !shouldSelectAll)
    })
    emit('permission-changed')
  } finally {
    // Xóa tất cả IDs khỏi changing set
    permissionIds.forEach((id) => changingPermissions.value.delete(id))
  }
}

// Lấy ra tất cả các id của permission trong group
const getAllPermissionIdsInGroup = (group) => {
  if (!group) {
    console.warn('⚠️ getAllPermissionIdsInGroup: group is null or undefined')
    return []
  }

  let ids = []

  // lấy id từ group hiện tại
  if (group.permissions && Array.isArray(group.permissions)) {
    const validPermissions = group.permissions.filter((p) => p && p.id)
    ids = ids.concat(validPermissions.map((p) => p.id))
  }

  // đệ quy vào children
  if (group.children && Array.isArray(group.children)) {
    group.children.forEach((child) => {
      if (child) {
        ids = ids.concat(getAllPermissionIdsInGroup(child))
      }
    })
  }

  return ids.filter((id, index, self) => self.indexOf(id) === index) // Remove duplicates
}

// Watch for permissions changes
watch(
  () => props.permissions,
  (newPermissions) => {
    // Xử lý dữ liệu mới từ API với cấu trúc { permission_groups: [...] }
    if (newPermissions) {
      if (Array.isArray(newPermissions)) {
        // Nếu là array (format cũ)
        localPermissions.value = JSON.parse(JSON.stringify(newPermissions))
      } else if (newPermissions.permission_groups && Array.isArray(newPermissions.permission_groups)) {
        // Nếu là object với permission_groups (format mới)
        localPermissions.value = JSON.parse(JSON.stringify(newPermissions.permission_groups))
      } else {
        localPermissions.value = []
      }
    } else {
      localPermissions.value = []
    }

  
    transformDataForTreeTable()
  },
  { immediate: true, deep: true },
)

watch(
  () => props.selectedRole,
  (newRole) => {
    // Reset expanded keys when role changes
    expandedKeys.value = []
    // Transform data for tree table
    transformDataForTreeTable()
  },
  { immediate: true },
)

const getPermissionByType = (row, type) => {
  if (!row || !row.permissions || !Array.isArray(row.permissions)) return null

  const permission = row.permissions.find((p) => p && p.type === type)
  if (!permission) return null

  return {
    ...permission,
    active: permission.active || false,
  }
}

const getPermissionActiveState = (row, type) => {
  if (!row || !type) return false
  const permission = getPermissionByType(row, type)
  return permission ? permission.active : false
}

const updateLocalPermissionState = (permissionId, active) => {
  if (!permissionId) {
    console.warn('⚠️ updateLocalPermissionState: permissionId is required')
    return false
  }

  // Update permission in the local permissions array
  const updatePermissionInGroups = (groups) => {
    if (!groups || !Array.isArray(groups)) return false

    for (const group of groups) {
      if (group && group.permissions && Array.isArray(group.permissions)) {
        const permission = group.permissions.find((p) => p && p.id === permissionId)
        if (permission) {
          permission.active = active
          console.log(`✅ Updated permission ${permissionId} to active: ${active}`)
          return true
        }
      }

      // Check children groups
      if (group && group.children && Array.isArray(group.children)) {
        if (updatePermissionInGroups(group.children)) {
          return true
        }
      }
    }
    return false
  }

  const updated1 = updatePermissionInGroups(localPermissions.value)

  // Also update table data
  const updatePermissionInTableData = (data) => {
    if (!data || !Array.isArray(data)) return false

    for (const row of data) {
      if (row && row.permissions && Array.isArray(row.permissions)) {
        const permission = row.permissions.find((p) => p && p.id === permissionId)
        if (permission) {
          permission.active = active
          return true
        }
      }

      if (row && row.children && Array.isArray(row.children)) {
        if (updatePermissionInTableData(row.children)) {
          return true
        }
      }
    }
    return false
  }

  const updated2 = updatePermissionInTableData(tableData.value)

  if (!updated1 && !updated2) {
    console.warn(`⚠️ Permission ${permissionId} not found in local state`)
    return false
  }

  return true
}

const handlePermissionChange = async (permissionId, checked) => {
  if (!props.selectedRole || !permissionId) return

  // Check if role is protected
  if (isRoleProtected.value) {
    ElMessage.warning('Không thể chỉnh sửa quyền hạn của vai trò được bảo vệ')
    return
  }

  // Add to changing permissions set
  changingPermissions.value.add(permissionId)

  try {
    // Cập nhật local state ngay lập tức để UI responsive
    updateLocalPermissionState(permissionId, checked)

    // Force reactivity update
    await nextTick()

    // Emit event immediately for real-time UI update
    emit('permission-changed')

    // Call API in background (non-blocking)
    if (checked) {
      assignPermissions(props.selectedRole.id, [permissionId])
        .catch((error) => {
          console.error('❌ Background permission assignment failed:', error)
          ElMessage.error('Không thể cập nhật quyền hạn')
          // Revert local state if API call failed
          updateLocalPermissionState(permissionId, !checked)
          emit('permission-changed')
        })
        .finally(() => {
          // Remove from changing permissions set
          changingPermissions.value.delete(permissionId)
        })
    } else {
      revokePermissions(props.selectedRole.id, [permissionId])
        .catch((error) => {
          console.error('❌ Background permission revocation failed:', error)
          ElMessage.error('Không thể cập nhật quyền hạn')
          // Revert local state if API call failed
          updateLocalPermissionState(permissionId, !checked)
          emit('permission-changed')
        })
        .finally(() => {
          // Remove from changing permissions set
          changingPermissions.value.delete(permissionId)
        })
    }
  } catch (error) {
    console.error('❌ Permission change error:', error)
    ElMessage.error('Không thể cập nhật quyền hạn')

    // Revert local state if API call failed
    updateLocalPermissionState(permissionId, !checked)
    await nextTick()
    emit('permission-changed')

    // Remove from changing permissions set
    changingPermissions.value.delete(permissionId)
  }
}

const isRowExpanded = (row) => {
  if (!row || !row.id) return false
  return expandedKeys.value.includes(row.id)
}

const toggleRowExpansion = (row) => {
  if (!row || !row.id) {
    return
  }

  if (isRowExpanded(row)) {
    // Collapse
    expandedKeys.value = expandedKeys.value.filter((key) => key !== row.id)
  } else {
    // Expand
    expandedKeys.value.push(row.id)

    // Ensure the expanded row is visible in the table viewport without scrolling the page
    nextTick(() => {
      const tableBody = tableRef.value?.$el?.querySelector('.el-table__body-wrapper')
      if (tableBody) {
        // Find the expanded row element
        const rowElement = tableBody.querySelector(`[data-row-key="${row.id}"]`)
        if (rowElement) {
          // Calculate if the row is visible in the table viewport
          const tableRect = tableBody.getBoundingClientRect()
          const rowRect = rowElement.getBoundingClientRect()

          // If row is below the visible area, scroll within table only
          if (rowRect.bottom > tableRect.bottom) {
            const scrollTop = rowElement.offsetTop - tableBody.offsetHeight / 2
            tableBody.scrollTo({
              top: scrollTop,
              behavior: 'smooth',
            })
          }

          // Prevent page scroll by focusing on table body
          tableBody.focus()
        }
      }
    })
  }
}

// Expose computed properties for parent components
defineExpose({
  activePermissionCount,
  isRoleProtected,
  hasDisabledCheckboxes,
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/modules/users/_permission-table.scss' as *;

/* Force hide default expand icons */
:deep(.el-table__expand-icon) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  pointer-events: none !important;
  clip: rect(0, 0, 0, 0) !important;
  clip-path: inset(50%) !important;
}

:deep(.el-table__expand-icon .el-icon) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  pointer-events: none !important;
  clip: rect(0, 0, 0, 0) !important;
  clip-path: inset(50%) !important;
}

:deep(.el-table__expand-icon .el-icon svg) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  pointer-events: none !important;
  clip: rect(0, 0, 0, 0) !important;
  clip-path: inset(50%) !important;
}

:deep(.el-table__expand-column) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  min-width: 0 !important;
  max-width: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  background: none !important;
  overflow: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  pointer-events: none !important;
  clip: rect(0, 0, 0, 0) !important;
  clip-path: inset(50%) !important;
  font-size: 0 !important;
  line-height: 0 !important;
}
</style>

<style scoped>
.role-list-scroll {
  /* Chỉ cuộn theo trục dọc, ẩn trục ngang để tránh 2 thanh cuộn */
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: 0 2px 12px rgba(59, 130, 246, 0.08);
  transition: box-shadow 0.2s;
  /* Cảm giác cuộn mượt mà trên iOS */
  -webkit-overflow-scrolling: touch;
  /* Ẩn scrollbar mặc định (Firefox/IE) */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

/* Ẩn scrollbar mặc định (WebKit) */
.role-list-scroll::-webkit-scrollbar {
  width: 0;
  height: 0;
  background: transparent;
}

/* Khi hover thì hiện scrollbar mỏng (WebKit + Firefox) */
.role-list-scroll:hover {
  scrollbar-width: thin; /* Firefox */
}
.role-list-scroll:hover::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.role-list-scroll::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 6px;
}
.role-list-scroll:hover::-webkit-scrollbar-thumb {
  background: #cbd5e1;
}
.dark .role-list-scroll:hover::-webkit-scrollbar-thumb {
  background: #334155;
}

/* Scrollbar ngang cho bảng: ẩn mặc định, hiện khi hover */
.custom-permission-table {
  /* Chỉ cho phép cuộn ngang ở vùng bảng */
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  /* Ẩn scrollbar mặc định (Firefox/IE) */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}
.custom-permission-table::-webkit-scrollbar {
  height: 0;
  width: 0;
  background: transparent;
}
.custom-permission-table:hover {
  scrollbar-width: thin; /* Firefox */
}
.custom-permission-table:hover::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}
.custom-permission-table::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 6px;
}
.custom-permission-table:hover::-webkit-scrollbar-thumb {
  background: #cbd5e1;
}
.dark .custom-permission-table:hover::-webkit-scrollbar-thumb {
  background: #334155;
}

/* Light mode */
.permission-card :deep(.el-card) {
  border-radius: 0.75rem;
  border: 1px solid #344054;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.permission-card :deep(.el-card__header) {
  background: #f9fafb;
  border-bottom: 1px solid #d1d5db;
}

/* Dark mode */
.dark .permission-card :deep(.el-card) {
  border: 1px solid #374151;
  background-color: #1f2937;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.dark .permission-card :deep(.el-card__header) {
  background: #0f172a !important;
  border-bottom: 1px solid #4b5563;
}

.dark .permission-card :deep(.el-card__body) {
  background-color: #1e293b !important;
}
</style>
